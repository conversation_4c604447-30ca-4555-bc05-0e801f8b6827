'use client'

import { useState, useRef, useEffect, useTransition, useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'
import { locales, LOCALE_CONFIG, type Locale } from '@/i18n'
import { LanguagePreference } from '@/lib/language-preference'
import { LanguageSwitchFeedbackManager } from './LanguageSwitchFeedback'

interface LanguageSwitcherProps {
  variant?: 'dropdown' | 'inline'
  showFlag?: boolean
  showNativeName?: boolean
  className?: string
  showFeedback?: boolean
  autoSavePreference?: boolean
}

export default function LanguageSwitcher({
  variant = 'dropdown',
  showFlag = true,
  showNativeName = true,
  className = '',
  showFeedback = true,
  autoSavePreference = true
}: LanguageSwitcherProps) {
  const t = useTranslations('navigation')
  const router = useRouter()
  const pathname = usePathname()
  const currentLocale = useLocale() as Locale
  const [isPending, startTransition] = useTransition()
  const [isOpen, setIsOpen] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const feedbackManager = useRef(LanguageSwitchFeedbackManager.getInstance())

  // 获取语言列表数据
  const languages = locales.map(locale => ({
    code: locale,
    name: LOCALE_CONFIG[locale].name,
    nativeName: LOCALE_CONFIG[locale].nativeName,
    flag: LOCALE_CONFIG[locale].flag,
    active: locale === currentLocale
  }))

  const currentLanguage = languages.find(lang => lang.active)

  // 处理语言切换
  const handleLanguageChange = useCallback((locale: Locale) => {
    if (locale === currentLocale || isPending) return

    setIsAnimating(true)

    startTransition(() => {
      // 保存语言偏好
      if (autoSavePreference) {
        LanguagePreference.save(locale)
      }

      // 构建新的路径
      const newPath = pathname.replace(`/${currentLocale}`, `/${locale}`)
      router.push(newPath)

      // 显示成功反馈
      if (showFeedback) {
        setTimeout(() => {
          feedbackManager.current.show(locale)
        }, 100)
      }

      setIsOpen(false)
      setIsAnimating(false)
    })
  }, [currentLocale, isPending, pathname, router, autoSavePreference, showFeedback])

  // 键盘导航和外部点击处理
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    function handleKeyDown(event: KeyboardEvent) {
      if (!isOpen) return

      switch (event.key) {
        case 'Escape':
          setIsOpen(false)
          break
        case 'ArrowDown':
          event.preventDefault()
          // 焦点移动到下一个语言选项
          const nextIndex = (languages.findIndex(lang => lang.active) + 1) % languages.length
          handleLanguageChange(languages[nextIndex].code)
          break
        case 'ArrowUp':
          event.preventDefault()
          // 焦点移动到上一个语言选项
          const prevIndex = (languages.findIndex(lang => lang.active) - 1 + languages.length) % languages.length
          handleLanguageChange(languages[prevIndex].code)
          break
        case 'Enter':
        case ' ':
          event.preventDefault()
          setIsOpen(false)
          break
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, languages, handleLanguageChange])

  // 内联样式渲染
  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {languages.map((language) => (
          <button
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={`
              px-3 py-1 rounded-md text-sm font-medium transition-colors
              ${language.active 
                ? 'bg-primary-100 text-primary-700' 
                : 'text-neutral-600 hover:text-neutral-900 hover:bg-neutral-100'
              }
            `}
            aria-label={`Switch to ${language.name}`}
          >
            {showFlag && <span className="mr-1">{language.flag}</span>}
            {showNativeName ? language.nativeName : language.name}
          </button>
        ))}
      </div>
    )
  }

  // 下拉菜单样式渲染
  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* 触发按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          language-switcher-button flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium
          text-neutral-700 hover:text-neutral-900 hover:bg-neutral-100
          transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500
          ${isPending || isAnimating ? 'loading' : ''}
        `}
        aria-label={t('language')}
        aria-expanded={isOpen}
        aria-haspopup="true"
        disabled={isPending || isAnimating}
      >
        {showFlag && currentLanguage && (
          <span className="text-lg">{currentLanguage.flag}</span>
        )}
        <span>
          {currentLanguage && (showNativeName ? currentLanguage.nativeName : currentLanguage.name)}
        </span>
        {isPending ? (
          <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : (
          <svg
            className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className={`
          language-switcher-dropdown absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5
          focus:outline-none z-50 entered
        `}>
          <div className="py-1" role="menu" aria-orientation="vertical">
            {languages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`
                  language-switcher-item w-full flex items-center px-4 py-2 text-sm transition-colors
                  ${language.active
                    ? 'active bg-primary-50 text-primary-700'
                    : 'text-neutral-700 hover:bg-neutral-100 hover:text-neutral-900'
                  }
                `}
                role="menuitem"
                aria-label={`Switch to ${language.name}`}
              >
                {showFlag && (
                  <span className="text-lg mr-3">{language.flag}</span>
                )}
                <div className="flex flex-col items-start">
                  <span className="font-medium">
                    {showNativeName ? language.nativeName : language.name}
                  </span>
                  {showNativeName && language.nativeName !== language.name && (
                    <span className="text-xs text-neutral-500">{language.name}</span>
                  )}
                </div>
                {language.active && (
                  <svg
                    className="ml-auto w-4 h-4 text-primary-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// 简化版语言切换器（仅显示标志）
export function LanguageSwitcherCompact({ className = '' }: { className?: string }) {
  return (
    <LanguageSwitcher
      variant="dropdown"
      showFlag={true}
      showNativeName={false}
      className={className}
    />
  )
}

// 移动端语言切换器
export function LanguageSwitcherMobile({ className = '' }: { className?: string }) {
  const t = useTranslations('navigation')
  const router = useRouter()
  const pathname = usePathname()
  const currentLocale = useLocale() as Locale
  const [isPending, startTransition] = useTransition()

  // 获取语言列表数据
  const languages = locales.map(locale => ({
    code: locale,
    name: LOCALE_CONFIG[locale].name,
    nativeName: LOCALE_CONFIG[locale].nativeName,
    flag: LOCALE_CONFIG[locale].flag,
    active: locale === currentLocale
  }))

  const handleLanguageChange = (locale: Locale) => {
    if (locale === currentLocale) return

    startTransition(() => {
      const newPath = pathname.replace(`/${currentLocale}`, `/${locale}`)
      router.push(newPath)
    })
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <h3 className="text-sm font-medium text-neutral-900 mb-3">
        {t('language')}
      </h3>
      {languages.map((language) => (
        <button
          key={language.code}
          onClick={() => handleLanguageChange(language.code)}
          className={`
            w-full flex items-center justify-between px-3 py-2 rounded-md text-sm
            transition-colors disabled:opacity-50
            ${language.active
              ? 'bg-primary-100 text-primary-700'
              : 'text-neutral-700 hover:bg-neutral-100'
            }
          `}
          disabled={isPending}
        >
          <div className="flex items-center">
            <span className="text-lg mr-3">{language.flag}</span>
            <span>{language.nativeName}</span>
          </div>
          {language.active && (
            <svg
              className="w-4 h-4 text-primary-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </button>
      ))}
    </div>
  )
}
