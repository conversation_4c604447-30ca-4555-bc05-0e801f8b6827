import {notFound} from 'next/navigation';
import {getRequestConfig} from 'next-intl/server';

// 支持的语言列表
export const locales = ['en', 'zh-CN', 'ja', 'es'] as const;
export type Locale = (typeof locales)[number];

// 默认语言
export const defaultLocale: Locale = 'en';

// 语言配置
export const LOCALE_CONFIG: Record<Locale, {
  name: string
  nativeName: string
  flag: string
  dir: 'ltr' | 'rtl'
}> = {
  'en': { 
    name: 'English', 
    nativeName: 'English',
    flag: '🇺🇸', 
    dir: 'ltr' 
  },
  'zh-CN': { 
    name: 'Chinese (Simplified)', 
    nativeName: '简体中文',
    flag: '🇨🇳', 
    dir: 'ltr' 
  },
  'ja': { 
    name: 'Japanese', 
    nativeName: '日本語',
    flag: '🇯🇵', 
    dir: 'ltr' 
  },
  'es': { 
    name: 'Spanish', 
    nativeName: 'Español',
    flag: '🇪🇸', 
    dir: 'ltr' 
  }
};

// next-intl 配置
export default getRequestConfig(async ({locale}) => {
  // 验证语言是否支持
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  try {
    // 动态导入翻译文件
    const messages = (await import(`../messages/${locale}.json`)).default;
    
    return {
      locale,
      messages,
      // 时区配置
      timeZone: 'UTC',
      // 现在时间（用于相对时间格式化）
      now: new Date(),
      // 格式化配置
      formats: {
        dateTime: {
          short: {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
          },
          long: {
            day: 'numeric',
            month: 'long',
            year: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
          }
        },
        number: {
          precise: {
            maximumFractionDigits: 5
          }
        },
        list: {
          enumeration: {
            style: 'long',
            type: 'conjunction'
          }
        }
      }
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);
    notFound();
  }
});

// 工具函数：检查是否为支持的语言
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

// 工具函数：获取语言配置
export function getLocaleConfig(locale: Locale) {
  return LOCALE_CONFIG[locale];
}

// 工具函数：获取语言的显示名称
export function getLocaleDisplayName(locale: Locale, displayLocale: Locale = 'en') {
  const config = LOCALE_CONFIG[locale];
  return displayLocale === locale ? config.nativeName : config.name;
}
