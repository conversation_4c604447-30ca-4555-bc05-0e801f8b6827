[{"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx": "1", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/page.tsx": "2", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/products/page.tsx": "3", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/contact/route.ts": "4", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/robots/route.ts": "5", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts": "6", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/en/page.tsx": "7", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx": "8", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/es/page.tsx": "9", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/ja/page.tsx": "10", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx": "11", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx": "12", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx": "13", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx": "14", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/forms/ContactForm.tsx": "15", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitcher.tsx": "16", "/Users/<USER>/Vibe-Code/test-web-1.0/src/i18n.ts": "17", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/content.ts": "18", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/email.ts": "19", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/i18n.ts": "20", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/seo.ts": "21", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/test-content.ts": "22", "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/contact.ts": "23", "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/content.ts": "24"}, {"size": 8974, "mtime": 1749921782386, "results": "25", "hashOfConfig": "26"}, {"size": 804, "mtime": 1749919231118, "results": "27", "hashOfConfig": "26"}, {"size": 7034, "mtime": 1749923649743, "results": "28", "hashOfConfig": "26"}, {"size": 5986, "mtime": 1749921260989, "results": "29", "hashOfConfig": "26"}, {"size": 701, "mtime": 1749921426715, "results": "30", "hashOfConfig": "26"}, {"size": 3939, "mtime": 1749921417016, "results": "31", "hashOfConfig": "26"}, {"size": 751, "mtime": 1749919419831, "results": "32", "hashOfConfig": "26"}, {"size": 7037, "mtime": 1749921482791, "results": "33", "hashOfConfig": "26"}, {"size": 763, "mtime": 1749919464198, "results": "34", "hashOfConfig": "26"}, {"size": 764, "mtime": 1749919447062, "results": "35", "hashOfConfig": "26"}, {"size": 7002, "mtime": 1749918189874, "results": "36", "hashOfConfig": "26"}, {"size": 5997, "mtime": 1749921452879, "results": "37", "hashOfConfig": "26"}, {"size": 134, "mtime": 1749919604910, "results": "38", "hashOfConfig": "26"}, {"size": 754, "mtime": 1749919262388, "results": "39", "hashOfConfig": "26"}, {"size": 11665, "mtime": 1749922875885, "results": "40", "hashOfConfig": "26"}, {"size": 8891, "mtime": 1749923802543, "results": "41", "hashOfConfig": "26"}, {"size": 2559, "mtime": 1749922455700, "results": "42", "hashOfConfig": "26"}, {"size": 1337, "mtime": 1749915822348, "results": "43", "hashOfConfig": "26"}, {"size": 9993, "mtime": 1749921640963, "results": "44", "hashOfConfig": "26"}, {"size": 7384, "mtime": 1749922784622, "results": "45", "hashOfConfig": "26"}, {"size": 8360, "mtime": 1749921392362, "results": "46", "hashOfConfig": "26"}, {"size": 1425, "mtime": 1749915620427, "results": "47", "hashOfConfig": "26"}, {"size": 1487, "mtime": 1749921112907, "results": "48", "hashOfConfig": "26"}, {"size": 4999, "mtime": 1749915348407, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "m5zqkg", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx", ["122", "123", "124", "125"], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/products/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/contact/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/robots/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/en/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx", ["126"], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/es/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/ja/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx", ["127", "128", "129"], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/forms/ContactForm.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/i18n.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/email.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/i18n.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/seo.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/test-content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/contact.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/content.ts", [], [], {"ruleId": "130", "severity": 2, "message": "131", "line": 54, "column": 130, "nodeType": "132", "messageId": "133", "suggestions": "134"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 54, "column": 181, "nodeType": "132", "messageId": "133", "suggestions": "135"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 117, "column": 27, "nodeType": "132", "messageId": "133", "suggestions": "136"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 189, "column": 154, "nodeType": "132", "messageId": "133", "suggestions": "137"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 40, "column": 15, "nodeType": "132", "messageId": "133", "suggestions": "138"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 22, "column": 29, "nodeType": "132", "messageId": "133", "suggestions": "139"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 22, "column": 49, "nodeType": "132", "messageId": "133", "suggestions": "140"}, {"ruleId": "130", "severity": 2, "message": "131", "line": 38, "column": 50, "nodeType": "132", "messageId": "133", "suggestions": "141"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["142", "143", "144", "145"], ["146", "147", "148", "149"], ["150", "151", "152", "153"], ["154", "155", "156", "157"], ["158", "159", "160", "161"], ["162", "163", "164", "165"], ["166", "167", "168", "169"], ["170", "171", "172", "173"], {"messageId": "174", "data": "175", "fix": "176", "desc": "177"}, {"messageId": "174", "data": "178", "fix": "179", "desc": "180"}, {"messageId": "174", "data": "181", "fix": "182", "desc": "183"}, {"messageId": "174", "data": "184", "fix": "185", "desc": "186"}, {"messageId": "174", "data": "187", "fix": "188", "desc": "177"}, {"messageId": "174", "data": "189", "fix": "190", "desc": "180"}, {"messageId": "174", "data": "191", "fix": "192", "desc": "183"}, {"messageId": "174", "data": "193", "fix": "194", "desc": "186"}, {"messageId": "174", "data": "195", "fix": "196", "desc": "177"}, {"messageId": "174", "data": "197", "fix": "198", "desc": "180"}, {"messageId": "174", "data": "199", "fix": "200", "desc": "183"}, {"messageId": "174", "data": "201", "fix": "202", "desc": "186"}, {"messageId": "174", "data": "203", "fix": "204", "desc": "177"}, {"messageId": "174", "data": "205", "fix": "206", "desc": "180"}, {"messageId": "174", "data": "207", "fix": "208", "desc": "183"}, {"messageId": "174", "data": "209", "fix": "210", "desc": "186"}, {"messageId": "174", "data": "211", "fix": "212", "desc": "177"}, {"messageId": "174", "data": "213", "fix": "214", "desc": "180"}, {"messageId": "174", "data": "215", "fix": "216", "desc": "183"}, {"messageId": "174", "data": "217", "fix": "218", "desc": "186"}, {"messageId": "174", "data": "219", "fix": "220", "desc": "177"}, {"messageId": "174", "data": "221", "fix": "222", "desc": "180"}, {"messageId": "174", "data": "223", "fix": "224", "desc": "183"}, {"messageId": "174", "data": "225", "fix": "226", "desc": "186"}, {"messageId": "174", "data": "227", "fix": "228", "desc": "177"}, {"messageId": "174", "data": "229", "fix": "230", "desc": "180"}, {"messageId": "174", "data": "231", "fix": "232", "desc": "183"}, {"messageId": "174", "data": "233", "fix": "234", "desc": "186"}, {"messageId": "174", "data": "235", "fix": "236", "desc": "177"}, {"messageId": "174", "data": "237", "fix": "238", "desc": "180"}, {"messageId": "174", "data": "239", "fix": "240", "desc": "183"}, {"messageId": "174", "data": "241", "fix": "242", "desc": "186"}, "replaceWithAlt", {"alt": "243"}, {"range": "244", "text": "245"}, "Replace with `&apos;`.", {"alt": "246"}, {"range": "247", "text": "248"}, "Replace with `&lsquo;`.", {"alt": "249"}, {"range": "250", "text": "251"}, "Replace with `&#39;`.", {"alt": "252"}, {"range": "253", "text": "254"}, "Replace with `&rsquo;`.", {"alt": "243"}, {"range": "255", "text": "256"}, {"alt": "246"}, {"range": "257", "text": "258"}, {"alt": "249"}, {"range": "259", "text": "260"}, {"alt": "252"}, {"range": "261", "text": "262"}, {"alt": "243"}, {"range": "263", "text": "264"}, {"alt": "246"}, {"range": "265", "text": "266"}, {"alt": "249"}, {"range": "267", "text": "268"}, {"alt": "252"}, {"range": "269", "text": "270"}, {"alt": "243"}, {"range": "271", "text": "272"}, {"alt": "246"}, {"range": "273", "text": "274"}, {"alt": "249"}, {"range": "275", "text": "276"}, {"alt": "252"}, {"range": "277", "text": "278"}, {"alt": "243"}, {"range": "279", "text": "280"}, {"alt": "246"}, {"range": "281", "text": "282"}, {"alt": "249"}, {"range": "283", "text": "284"}, {"alt": "252"}, {"range": "285", "text": "286"}, {"alt": "243"}, {"range": "287", "text": "288"}, {"alt": "246"}, {"range": "289", "text": "290"}, {"alt": "249"}, {"range": "291", "text": "292"}, {"alt": "252"}, {"range": "293", "text": "294"}, {"alt": "243"}, {"range": "295", "text": "296"}, {"alt": "246"}, {"range": "297", "text": "298"}, {"alt": "249"}, {"range": "299", "text": "300"}, {"alt": "252"}, {"range": "301", "text": "302"}, {"alt": "243"}, {"range": "303", "text": "304"}, {"alt": "246"}, {"range": "305", "text": "306"}, {"alt": "249"}, {"range": "307", "text": "308"}, {"alt": "252"}, {"range": "309", "text": "310"}, "&apos;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&apos;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", "&lsquo;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&lsquo;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", "&#39;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&#39;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", "&rsquo;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&rsquo;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&apos;re here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&lsquo;re here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&#39;re here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&rsquo;re here to assist.\n                  ", [5116, 5277], "\n                    If you&apos;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [5116, 5277], "\n                    If you&lsquo;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [5116, 5277], "\n                    If you&#39;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [5116, 5277], "\n                    If you&rsquo;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&apos;re interested in.\n                ", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&lsquo;re interested in.\n                ", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&#39;re interested in.\n                ", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&rsquo;re interested in.\n                ", [1290, 1434], "\n            We&apos;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [1290, 1434], "\n            We&lsquo;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [1290, 1434], "\n            We&#39;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [1290, 1434], "\n            We&rsquo;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [792, 952], "\n            Sorry, we couldn&apos;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn&lsquo;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn&#39;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn&rsquo;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&apos;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&lsquo;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&#39;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&rsquo;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [1535, 1617], "\n                  Use our search to find what you&apos;re looking for\n                ", [1535, 1617], "\n                  Use our search to find what you&lsquo;re looking for\n                ", [1535, 1617], "\n                  Use our search to find what you&#39;re looking for\n                ", [1535, 1617], "\n                  Use our search to find what you&rsquo;re looking for\n                "]