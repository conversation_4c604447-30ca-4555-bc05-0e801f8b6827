[{"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx": "1", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/page.tsx": "2", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/products/page.tsx": "3", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/contact/route.ts": "4", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/robots/route.ts": "5", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts": "6", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/en/page.tsx": "7", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx": "8", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/es/page.tsx": "9", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/ja/page.tsx": "10", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx": "11", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx": "12", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx": "13", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx": "14", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/forms/ContactForm.tsx": "15", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitcher.tsx": "16", "/Users/<USER>/Vibe-Code/test-web-1.0/src/i18n.ts": "17", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/content.ts": "18", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/email.ts": "19", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/i18n.ts": "20", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/seo.ts": "21", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/test-content.ts": "22", "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/contact.ts": "23", "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/content.ts": "24", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitchFeedback.tsx": "25", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/language-preference.ts": "26"}, {"size": 8974, "mtime": 1749921782386, "results": "27", "hashOfConfig": "28"}, {"size": 804, "mtime": 1749919231118, "results": "29", "hashOfConfig": "28"}, {"size": 7034, "mtime": 1749923649743, "results": "30", "hashOfConfig": "28"}, {"size": 5986, "mtime": 1749921260989, "results": "31", "hashOfConfig": "28"}, {"size": 701, "mtime": 1749921426715, "results": "32", "hashOfConfig": "28"}, {"size": 3939, "mtime": 1749921417016, "results": "33", "hashOfConfig": "28"}, {"size": 751, "mtime": 1749919419831, "results": "34", "hashOfConfig": "28"}, {"size": 7037, "mtime": 1749921482791, "results": "35", "hashOfConfig": "28"}, {"size": 763, "mtime": 1749919464198, "results": "36", "hashOfConfig": "28"}, {"size": 764, "mtime": 1749919447062, "results": "37", "hashOfConfig": "28"}, {"size": 7002, "mtime": 1749918189874, "results": "38", "hashOfConfig": "28"}, {"size": 5997, "mtime": 1749921452879, "results": "39", "hashOfConfig": "28"}, {"size": 134, "mtime": 1749919604910, "results": "40", "hashOfConfig": "28"}, {"size": 754, "mtime": 1749919262388, "results": "41", "hashOfConfig": "28"}, {"size": 11665, "mtime": 1749922875885, "results": "42", "hashOfConfig": "28"}, {"size": 10907, "mtime": 1749924486100, "results": "43", "hashOfConfig": "28"}, {"size": 2573, "mtime": 1749923930619, "results": "44", "hashOfConfig": "28"}, {"size": 1337, "mtime": 1749915822348, "results": "45", "hashOfConfig": "28"}, {"size": 9993, "mtime": 1749921640963, "results": "46", "hashOfConfig": "28"}, {"size": 7384, "mtime": 1749922784622, "results": "47", "hashOfConfig": "28"}, {"size": 8360, "mtime": 1749921392362, "results": "48", "hashOfConfig": "28"}, {"size": 1425, "mtime": 1749915620427, "results": "49", "hashOfConfig": "28"}, {"size": 1487, "mtime": 1749921112907, "results": "50", "hashOfConfig": "28"}, {"size": 4999, "mtime": 1749915348407, "results": "51", "hashOfConfig": "28"}, {"size": 3051, "mtime": 1749924327991, "results": "52", "hashOfConfig": "28"}, {"size": 3809, "mtime": 1749924306260, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "m5zqkg", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx", ["132", "133", "134", "135"], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/products/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/contact/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/robots/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/en/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx", ["136"], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/es/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/ja/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx", ["137", "138", "139"], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/forms/ContactForm.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/i18n.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/email.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/i18n.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/seo.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/test-content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/contact.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitchFeedback.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/language-preference.ts", [], [], {"ruleId": "140", "severity": 2, "message": "141", "line": 54, "column": 130, "nodeType": "142", "messageId": "143", "suggestions": "144"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 54, "column": 181, "nodeType": "142", "messageId": "143", "suggestions": "145"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 117, "column": 27, "nodeType": "142", "messageId": "143", "suggestions": "146"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 189, "column": 154, "nodeType": "142", "messageId": "143", "suggestions": "147"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 40, "column": 15, "nodeType": "142", "messageId": "143", "suggestions": "148"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 22, "column": 29, "nodeType": "142", "messageId": "143", "suggestions": "149"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 22, "column": 49, "nodeType": "142", "messageId": "143", "suggestions": "150"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 38, "column": 50, "nodeType": "142", "messageId": "143", "suggestions": "151"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["152", "153", "154", "155"], ["156", "157", "158", "159"], ["160", "161", "162", "163"], ["164", "165", "166", "167"], ["168", "169", "170", "171"], ["172", "173", "174", "175"], ["176", "177", "178", "179"], ["180", "181", "182", "183"], {"messageId": "184", "data": "185", "fix": "186", "desc": "187"}, {"messageId": "184", "data": "188", "fix": "189", "desc": "190"}, {"messageId": "184", "data": "191", "fix": "192", "desc": "193"}, {"messageId": "184", "data": "194", "fix": "195", "desc": "196"}, {"messageId": "184", "data": "197", "fix": "198", "desc": "187"}, {"messageId": "184", "data": "199", "fix": "200", "desc": "190"}, {"messageId": "184", "data": "201", "fix": "202", "desc": "193"}, {"messageId": "184", "data": "203", "fix": "204", "desc": "196"}, {"messageId": "184", "data": "205", "fix": "206", "desc": "187"}, {"messageId": "184", "data": "207", "fix": "208", "desc": "190"}, {"messageId": "184", "data": "209", "fix": "210", "desc": "193"}, {"messageId": "184", "data": "211", "fix": "212", "desc": "196"}, {"messageId": "184", "data": "213", "fix": "214", "desc": "187"}, {"messageId": "184", "data": "215", "fix": "216", "desc": "190"}, {"messageId": "184", "data": "217", "fix": "218", "desc": "193"}, {"messageId": "184", "data": "219", "fix": "220", "desc": "196"}, {"messageId": "184", "data": "221", "fix": "222", "desc": "187"}, {"messageId": "184", "data": "223", "fix": "224", "desc": "190"}, {"messageId": "184", "data": "225", "fix": "226", "desc": "193"}, {"messageId": "184", "data": "227", "fix": "228", "desc": "196"}, {"messageId": "184", "data": "229", "fix": "230", "desc": "187"}, {"messageId": "184", "data": "231", "fix": "232", "desc": "190"}, {"messageId": "184", "data": "233", "fix": "234", "desc": "193"}, {"messageId": "184", "data": "235", "fix": "236", "desc": "196"}, {"messageId": "184", "data": "237", "fix": "238", "desc": "187"}, {"messageId": "184", "data": "239", "fix": "240", "desc": "190"}, {"messageId": "184", "data": "241", "fix": "242", "desc": "193"}, {"messageId": "184", "data": "243", "fix": "244", "desc": "196"}, {"messageId": "184", "data": "245", "fix": "246", "desc": "187"}, {"messageId": "184", "data": "247", "fix": "248", "desc": "190"}, {"messageId": "184", "data": "249", "fix": "250", "desc": "193"}, {"messageId": "184", "data": "251", "fix": "252", "desc": "196"}, "replaceWithAlt", {"alt": "253"}, {"range": "254", "text": "255"}, "Replace with `&apos;`.", {"alt": "256"}, {"range": "257", "text": "258"}, "Replace with `&lsquo;`.", {"alt": "259"}, {"range": "260", "text": "261"}, "Replace with `&#39;`.", {"alt": "262"}, {"range": "263", "text": "264"}, "Replace with `&rsquo;`.", {"alt": "253"}, {"range": "265", "text": "266"}, {"alt": "256"}, {"range": "267", "text": "268"}, {"alt": "259"}, {"range": "269", "text": "270"}, {"alt": "262"}, {"range": "271", "text": "272"}, {"alt": "253"}, {"range": "273", "text": "274"}, {"alt": "256"}, {"range": "275", "text": "276"}, {"alt": "259"}, {"range": "277", "text": "278"}, {"alt": "262"}, {"range": "279", "text": "280"}, {"alt": "253"}, {"range": "281", "text": "282"}, {"alt": "256"}, {"range": "283", "text": "284"}, {"alt": "259"}, {"range": "285", "text": "286"}, {"alt": "262"}, {"range": "287", "text": "288"}, {"alt": "253"}, {"range": "289", "text": "290"}, {"alt": "256"}, {"range": "291", "text": "292"}, {"alt": "259"}, {"range": "293", "text": "294"}, {"alt": "262"}, {"range": "295", "text": "296"}, {"alt": "253"}, {"range": "297", "text": "298"}, {"alt": "256"}, {"range": "299", "text": "300"}, {"alt": "259"}, {"range": "301", "text": "302"}, {"alt": "262"}, {"range": "303", "text": "304"}, {"alt": "253"}, {"range": "305", "text": "306"}, {"alt": "256"}, {"range": "307", "text": "308"}, {"alt": "259"}, {"range": "309", "text": "310"}, {"alt": "262"}, {"range": "311", "text": "312"}, {"alt": "253"}, {"range": "313", "text": "314"}, {"alt": "256"}, {"range": "315", "text": "316"}, {"alt": "259"}, {"range": "317", "text": "318"}, {"alt": "262"}, {"range": "319", "text": "320"}, "&apos;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&apos;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", "&lsquo;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&lsquo;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", "&#39;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&#39;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", "&rsquo;", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&rsquo;re dealing with an emergency or planning ahead, we're here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&apos;re here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&lsquo;re here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&#39;re here to assist.\n                  ", [2115, 2334], "\n                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we&rsquo;re here to assist.\n                  ", [5116, 5277], "\n                    If you&apos;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [5116, 5277], "\n                    If you&lsquo;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [5116, 5277], "\n                    If you&#39;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [5116, 5277], "\n                    If you&rsquo;re experiencing active flooding or an emergency situation, \n                    please call our 24/7 emergency hotline immediately at", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&apos;re interested in.\n                ", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&lsquo;re interested in.\n                ", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&#39;re interested in.\n                ", [8132, 8321], "\n                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&rsquo;re interested in.\n                ", [1290, 1434], "\n            We&apos;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [1290, 1434], "\n            We&lsquo;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [1290, 1434], "\n            We&#39;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [1290, 1434], "\n            We&rsquo;re sorry, but something unexpected happened. \n            Our team has been notified and is working to fix the issue.\n          ", [792, 952], "\n            Sorry, we couldn&apos;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn&lsquo;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn&#39;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn&rsquo;t find the page you're looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&apos;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&lsquo;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&#39;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [792, 952], "\n            Sorry, we couldn't find the page you&rsquo;re looking for. \n            The page might have been moved, deleted, or you entered the wrong URL.\n          ", [1535, 1617], "\n                  Use our search to find what you&apos;re looking for\n                ", [1535, 1617], "\n                  Use our search to find what you&lsquo;re looking for\n                ", [1535, 1617], "\n                  Use our search to find what you&#39;re looking for\n                ", [1535, 1617], "\n                  Use our search to find what you&rsquo;re looking for\n                "]