"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/htmlparser2@8.0.2";
exports.ids = ["vendor-chunks/htmlparser2@8.0.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Parser.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Parser.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/decode.js\");\n\n\nconst formTags = new Set([\n    \"input\",\n    \"option\",\n    \"optgroup\",\n    \"select\",\n    \"button\",\n    \"datalist\",\n    \"textarea\",\n]);\nconst pTag = new Set([\"p\"]);\nconst tableSectionTags = new Set([\"thead\", \"tbody\"]);\nconst ddtTags = new Set([\"dd\", \"dt\"]);\nconst rtpTags = new Set([\"rt\", \"rp\"]);\nconst openImpliesClose = new Map([\n    [\"tr\", new Set([\"tr\", \"th\", \"td\"])],\n    [\"th\", new Set([\"th\"])],\n    [\"td\", new Set([\"thead\", \"th\", \"td\"])],\n    [\"body\", new Set([\"head\", \"link\", \"script\"])],\n    [\"li\", new Set([\"li\"])],\n    [\"p\", pTag],\n    [\"h1\", pTag],\n    [\"h2\", pTag],\n    [\"h3\", pTag],\n    [\"h4\", pTag],\n    [\"h5\", pTag],\n    [\"h6\", pTag],\n    [\"select\", formTags],\n    [\"input\", formTags],\n    [\"output\", formTags],\n    [\"button\", formTags],\n    [\"datalist\", formTags],\n    [\"textarea\", formTags],\n    [\"option\", new Set([\"option\"])],\n    [\"optgroup\", new Set([\"optgroup\", \"option\"])],\n    [\"dd\", ddtTags],\n    [\"dt\", ddtTags],\n    [\"address\", pTag],\n    [\"article\", pTag],\n    [\"aside\", pTag],\n    [\"blockquote\", pTag],\n    [\"details\", pTag],\n    [\"div\", pTag],\n    [\"dl\", pTag],\n    [\"fieldset\", pTag],\n    [\"figcaption\", pTag],\n    [\"figure\", pTag],\n    [\"footer\", pTag],\n    [\"form\", pTag],\n    [\"header\", pTag],\n    [\"hr\", pTag],\n    [\"main\", pTag],\n    [\"nav\", pTag],\n    [\"ol\", pTag],\n    [\"pre\", pTag],\n    [\"section\", pTag],\n    [\"table\", pTag],\n    [\"ul\", pTag],\n    [\"rt\", rtpTags],\n    [\"rp\", rtpTags],\n    [\"tbody\", tableSectionTags],\n    [\"tfoot\", tableSectionTags],\n]);\nconst voidElements = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\nconst foreignContextElements = new Set([\"math\", \"svg\"]);\nconst htmlIntegrationElements = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignobject\",\n    \"desc\",\n    \"title\",\n]);\nconst reNameEnd = /\\s|\\//;\nclass Parser {\n    constructor(cbs, options = {}) {\n        var _a, _b, _c, _d, _e;\n        this.options = options;\n        /** The start index of the last event. */\n        this.startIndex = 0;\n        /** The end index of the last event. */\n        this.endIndex = 0;\n        /**\n         * Store the start index of the current open tag,\n         * so we can update the start index for attributes.\n         */\n        this.openTagStart = 0;\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribvalue = \"\";\n        this.attribs = null;\n        this.stack = [];\n        this.foreignContext = [];\n        this.buffers = [];\n        this.bufferOffset = 0;\n        /** The index of the last written buffer. Used when resuming after a `pause()`. */\n        this.writeIndex = 0;\n        /** Indicates whether the parser has finished running / `.end` has been called. */\n        this.ended = false;\n        this.cbs = cbs !== null && cbs !== void 0 ? cbs : {};\n        this.lowerCaseTagNames = (_a = options.lowerCaseTags) !== null && _a !== void 0 ? _a : !options.xmlMode;\n        this.lowerCaseAttributeNames =\n            (_b = options.lowerCaseAttributeNames) !== null && _b !== void 0 ? _b : !options.xmlMode;\n        this.tokenizer = new ((_c = options.Tokenizer) !== null && _c !== void 0 ? _c : _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.options, this);\n        (_e = (_d = this.cbs).onparserinit) === null || _e === void 0 ? void 0 : _e.call(_d, this);\n    }\n    // Tokenizer event handlers\n    /** @internal */\n    ontext(start, endIndex) {\n        var _a, _b;\n        const data = this.getSlice(start, endIndex);\n        this.endIndex = endIndex - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, data);\n        this.startIndex = endIndex;\n    }\n    /** @internal */\n    ontextentity(cp) {\n        var _a, _b;\n        /*\n         * Entities can be emitted on the character, or directly after.\n         * We use the section start here to get accurate indices.\n         */\n        const index = this.tokenizer.getSectionStart();\n        this.endIndex = index - 1;\n        (_b = (_a = this.cbs).ontext) === null || _b === void 0 ? void 0 : _b.call(_a, (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp));\n        this.startIndex = index;\n    }\n    isVoidElement(name) {\n        return !this.options.xmlMode && voidElements.has(name);\n    }\n    /** @internal */\n    onopentagname(start, endIndex) {\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        this.emitOpenTag(name);\n    }\n    emitOpenTag(name) {\n        var _a, _b, _c, _d;\n        this.openTagStart = this.startIndex;\n        this.tagname = name;\n        const impliesClose = !this.options.xmlMode && openImpliesClose.get(name);\n        if (impliesClose) {\n            while (this.stack.length > 0 &&\n                impliesClose.has(this.stack[this.stack.length - 1])) {\n                const element = this.stack.pop();\n                (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, element, true);\n            }\n        }\n        if (!this.isVoidElement(name)) {\n            this.stack.push(name);\n            if (foreignContextElements.has(name)) {\n                this.foreignContext.push(true);\n            }\n            else if (htmlIntegrationElements.has(name)) {\n                this.foreignContext.push(false);\n            }\n        }\n        (_d = (_c = this.cbs).onopentagname) === null || _d === void 0 ? void 0 : _d.call(_c, name);\n        if (this.cbs.onopentag)\n            this.attribs = {};\n    }\n    endOpenTag(isImplied) {\n        var _a, _b;\n        this.startIndex = this.openTagStart;\n        if (this.attribs) {\n            (_b = (_a = this.cbs).onopentag) === null || _b === void 0 ? void 0 : _b.call(_a, this.tagname, this.attribs, isImplied);\n            this.attribs = null;\n        }\n        if (this.cbs.onclosetag && this.isVoidElement(this.tagname)) {\n            this.cbs.onclosetag(this.tagname, true);\n        }\n        this.tagname = \"\";\n    }\n    /** @internal */\n    onopentagend(endIndex) {\n        this.endIndex = endIndex;\n        this.endOpenTag(false);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onclosetag(start, endIndex) {\n        var _a, _b, _c, _d, _e, _f;\n        this.endIndex = endIndex;\n        let name = this.getSlice(start, endIndex);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        if (foreignContextElements.has(name) ||\n            htmlIntegrationElements.has(name)) {\n            this.foreignContext.pop();\n        }\n        if (!this.isVoidElement(name)) {\n            const pos = this.stack.lastIndexOf(name);\n            if (pos !== -1) {\n                if (this.cbs.onclosetag) {\n                    let count = this.stack.length - pos;\n                    while (count--) {\n                        // We know the stack has sufficient elements.\n                        this.cbs.onclosetag(this.stack.pop(), count !== 0);\n                    }\n                }\n                else\n                    this.stack.length = pos;\n            }\n            else if (!this.options.xmlMode && name === \"p\") {\n                // Implicit open before close\n                this.emitOpenTag(\"p\");\n                this.closeCurrentTag(true);\n            }\n        }\n        else if (!this.options.xmlMode && name === \"br\") {\n            // We can't use `emitOpenTag` for implicit open, as `br` would be implicitly closed.\n            (_b = (_a = this.cbs).onopentagname) === null || _b === void 0 ? void 0 : _b.call(_a, \"br\");\n            (_d = (_c = this.cbs).onopentag) === null || _d === void 0 ? void 0 : _d.call(_c, \"br\", {}, true);\n            (_f = (_e = this.cbs).onclosetag) === null || _f === void 0 ? void 0 : _f.call(_e, \"br\", false);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onselfclosingtag(endIndex) {\n        this.endIndex = endIndex;\n        if (this.options.xmlMode ||\n            this.options.recognizeSelfClosing ||\n            this.foreignContext[this.foreignContext.length - 1]) {\n            this.closeCurrentTag(false);\n            // Set `startIndex` for next node\n            this.startIndex = endIndex + 1;\n        }\n        else {\n            // Ignore the fact that the tag is self-closing.\n            this.onopentagend(endIndex);\n        }\n    }\n    closeCurrentTag(isOpenImplied) {\n        var _a, _b;\n        const name = this.tagname;\n        this.endOpenTag(isOpenImplied);\n        // Self-closing tags will be on the top of the stack\n        if (this.stack[this.stack.length - 1] === name) {\n            // If the opening tag isn't implied, the closing tag has to be implied.\n            (_b = (_a = this.cbs).onclosetag) === null || _b === void 0 ? void 0 : _b.call(_a, name, !isOpenImplied);\n            this.stack.pop();\n        }\n    }\n    /** @internal */\n    onattribname(start, endIndex) {\n        this.startIndex = start;\n        const name = this.getSlice(start, endIndex);\n        this.attribname = this.lowerCaseAttributeNames\n            ? name.toLowerCase()\n            : name;\n    }\n    /** @internal */\n    onattribdata(start, endIndex) {\n        this.attribvalue += this.getSlice(start, endIndex);\n    }\n    /** @internal */\n    onattribentity(cp) {\n        this.attribvalue += (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(cp);\n    }\n    /** @internal */\n    onattribend(quote, endIndex) {\n        var _a, _b;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).onattribute) === null || _b === void 0 ? void 0 : _b.call(_a, this.attribname, this.attribvalue, quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Double\n            ? '\"'\n            : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.Single\n                ? \"'\"\n                : quote === _Tokenizer_js__WEBPACK_IMPORTED_MODULE_0__.QuoteType.NoValue\n                    ? undefined\n                    : null);\n        if (this.attribs &&\n            !Object.prototype.hasOwnProperty.call(this.attribs, this.attribname)) {\n            this.attribs[this.attribname] = this.attribvalue;\n        }\n        this.attribvalue = \"\";\n    }\n    getInstructionName(value) {\n        const index = value.search(reNameEnd);\n        let name = index < 0 ? value : value.substr(0, index);\n        if (this.lowerCaseTagNames) {\n            name = name.toLowerCase();\n        }\n        return name;\n    }\n    /** @internal */\n    ondeclaration(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`!${name}`, `!${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onprocessinginstruction(start, endIndex) {\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex);\n        if (this.cbs.onprocessinginstruction) {\n            const name = this.getInstructionName(value);\n            this.cbs.onprocessinginstruction(`?${name}`, `?${value}`);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncomment(start, endIndex, offset) {\n        var _a, _b, _c, _d;\n        this.endIndex = endIndex;\n        (_b = (_a = this.cbs).oncomment) === null || _b === void 0 ? void 0 : _b.call(_a, this.getSlice(start, endIndex - offset));\n        (_d = (_c = this.cbs).oncommentend) === null || _d === void 0 ? void 0 : _d.call(_c);\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    oncdata(start, endIndex, offset) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n        this.endIndex = endIndex;\n        const value = this.getSlice(start, endIndex - offset);\n        if (this.options.xmlMode || this.options.recognizeCDATA) {\n            (_b = (_a = this.cbs).oncdatastart) === null || _b === void 0 ? void 0 : _b.call(_a);\n            (_d = (_c = this.cbs).ontext) === null || _d === void 0 ? void 0 : _d.call(_c, value);\n            (_f = (_e = this.cbs).oncdataend) === null || _f === void 0 ? void 0 : _f.call(_e);\n        }\n        else {\n            (_h = (_g = this.cbs).oncomment) === null || _h === void 0 ? void 0 : _h.call(_g, `[CDATA[${value}]]`);\n            (_k = (_j = this.cbs).oncommentend) === null || _k === void 0 ? void 0 : _k.call(_j);\n        }\n        // Set `startIndex` for next node\n        this.startIndex = endIndex + 1;\n    }\n    /** @internal */\n    onend() {\n        var _a, _b;\n        if (this.cbs.onclosetag) {\n            // Set the end index for all remaining tags\n            this.endIndex = this.startIndex;\n            for (let index = this.stack.length; index > 0; this.cbs.onclosetag(this.stack[--index], true))\n                ;\n        }\n        (_b = (_a = this.cbs).onend) === null || _b === void 0 ? void 0 : _b.call(_a);\n    }\n    /**\n     * Resets the parser to a blank state, ready to parse a new HTML document\n     */\n    reset() {\n        var _a, _b, _c, _d;\n        (_b = (_a = this.cbs).onreset) === null || _b === void 0 ? void 0 : _b.call(_a);\n        this.tokenizer.reset();\n        this.tagname = \"\";\n        this.attribname = \"\";\n        this.attribs = null;\n        this.stack.length = 0;\n        this.startIndex = 0;\n        this.endIndex = 0;\n        (_d = (_c = this.cbs).onparserinit) === null || _d === void 0 ? void 0 : _d.call(_c, this);\n        this.buffers.length = 0;\n        this.bufferOffset = 0;\n        this.writeIndex = 0;\n        this.ended = false;\n    }\n    /**\n     * Resets the parser, then parses a complete document and\n     * pushes it to the handler.\n     *\n     * @param data Document to parse.\n     */\n    parseComplete(data) {\n        this.reset();\n        this.end(data);\n    }\n    getSlice(start, end) {\n        while (start - this.bufferOffset >= this.buffers[0].length) {\n            this.shiftBuffer();\n        }\n        let slice = this.buffers[0].slice(start - this.bufferOffset, end - this.bufferOffset);\n        while (end - this.bufferOffset > this.buffers[0].length) {\n            this.shiftBuffer();\n            slice += this.buffers[0].slice(0, end - this.bufferOffset);\n        }\n        return slice;\n    }\n    shiftBuffer() {\n        this.bufferOffset += this.buffers[0].length;\n        this.writeIndex--;\n        this.buffers.shift();\n    }\n    /**\n     * Parses a chunk of data and calls the corresponding callbacks.\n     *\n     * @param chunk Chunk to parse.\n     */\n    write(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".write() after done!\"));\n            return;\n        }\n        this.buffers.push(chunk);\n        if (this.tokenizer.running) {\n            this.tokenizer.write(chunk);\n            this.writeIndex++;\n        }\n    }\n    /**\n     * Parses the end of the buffer and clears the stack, calls onend.\n     *\n     * @param chunk Optional final chunk to parse.\n     */\n    end(chunk) {\n        var _a, _b;\n        if (this.ended) {\n            (_b = (_a = this.cbs).onerror) === null || _b === void 0 ? void 0 : _b.call(_a, new Error(\".end() after done!\"));\n            return;\n        }\n        if (chunk)\n            this.write(chunk);\n        this.ended = true;\n        this.tokenizer.end();\n    }\n    /**\n     * Pauses parsing. The parser won't emit events until `resume` is called.\n     */\n    pause() {\n        this.tokenizer.pause();\n    }\n    /**\n     * Resumes parsing after `pause` was called.\n     */\n    resume() {\n        this.tokenizer.resume();\n        while (this.tokenizer.running &&\n            this.writeIndex < this.buffers.length) {\n            this.tokenizer.write(this.buffers[this.writeIndex++]);\n        }\n        if (this.ended)\n            this.tokenizer.end();\n    }\n    /**\n     * Alias of `write`, for backwards compatibility.\n     *\n     * @param chunk Chunk to parse.\n     * @deprecated\n     */\n    parseChunk(chunk) {\n        this.write(chunk);\n    }\n    /**\n     * Alias of `end`, for backwards compatibility.\n     *\n     * @param chunk Optional final chunk to parse.\n     * @deprecated\n     */\n    done(chunk) {\n        this.end(chunk);\n    }\n}\n//# sourceMappingURL=Parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Tokenizer.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Tokenizer.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuoteType: () => (/* binding */ QuoteType),\n/* harmony export */   \"default\": () => (/* binding */ Tokenizer)\n/* harmony export */ });\n/* harmony import */ var entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! entities/lib/decode.js */ \"(rsc)/./node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/decode.js\");\n\nvar CharCodes;\n(function (CharCodes) {\n    CharCodes[CharCodes[\"Tab\"] = 9] = \"Tab\";\n    CharCodes[CharCodes[\"NewLine\"] = 10] = \"NewLine\";\n    CharCodes[CharCodes[\"FormFeed\"] = 12] = \"FormFeed\";\n    CharCodes[CharCodes[\"CarriageReturn\"] = 13] = \"CarriageReturn\";\n    CharCodes[CharCodes[\"Space\"] = 32] = \"Space\";\n    CharCodes[CharCodes[\"ExclamationMark\"] = 33] = \"ExclamationMark\";\n    CharCodes[CharCodes[\"Number\"] = 35] = \"Number\";\n    CharCodes[CharCodes[\"Amp\"] = 38] = \"Amp\";\n    CharCodes[CharCodes[\"SingleQuote\"] = 39] = \"SingleQuote\";\n    CharCodes[CharCodes[\"DoubleQuote\"] = 34] = \"DoubleQuote\";\n    CharCodes[CharCodes[\"Dash\"] = 45] = \"Dash\";\n    CharCodes[CharCodes[\"Slash\"] = 47] = \"Slash\";\n    CharCodes[CharCodes[\"Zero\"] = 48] = \"Zero\";\n    CharCodes[CharCodes[\"Nine\"] = 57] = \"Nine\";\n    CharCodes[CharCodes[\"Semi\"] = 59] = \"Semi\";\n    CharCodes[CharCodes[\"Lt\"] = 60] = \"Lt\";\n    CharCodes[CharCodes[\"Eq\"] = 61] = \"Eq\";\n    CharCodes[CharCodes[\"Gt\"] = 62] = \"Gt\";\n    CharCodes[CharCodes[\"Questionmark\"] = 63] = \"Questionmark\";\n    CharCodes[CharCodes[\"UpperA\"] = 65] = \"UpperA\";\n    CharCodes[CharCodes[\"LowerA\"] = 97] = \"LowerA\";\n    CharCodes[CharCodes[\"UpperF\"] = 70] = \"UpperF\";\n    CharCodes[CharCodes[\"LowerF\"] = 102] = \"LowerF\";\n    CharCodes[CharCodes[\"UpperZ\"] = 90] = \"UpperZ\";\n    CharCodes[CharCodes[\"LowerZ\"] = 122] = \"LowerZ\";\n    CharCodes[CharCodes[\"LowerX\"] = 120] = \"LowerX\";\n    CharCodes[CharCodes[\"OpeningSquareBracket\"] = 91] = \"OpeningSquareBracket\";\n})(CharCodes || (CharCodes = {}));\n/** All the states the tokenizer can be in. */\nvar State;\n(function (State) {\n    State[State[\"Text\"] = 1] = \"Text\";\n    State[State[\"BeforeTagName\"] = 2] = \"BeforeTagName\";\n    State[State[\"InTagName\"] = 3] = \"InTagName\";\n    State[State[\"InSelfClosingTag\"] = 4] = \"InSelfClosingTag\";\n    State[State[\"BeforeClosingTagName\"] = 5] = \"BeforeClosingTagName\";\n    State[State[\"InClosingTagName\"] = 6] = \"InClosingTagName\";\n    State[State[\"AfterClosingTagName\"] = 7] = \"AfterClosingTagName\";\n    // Attributes\n    State[State[\"BeforeAttributeName\"] = 8] = \"BeforeAttributeName\";\n    State[State[\"InAttributeName\"] = 9] = \"InAttributeName\";\n    State[State[\"AfterAttributeName\"] = 10] = \"AfterAttributeName\";\n    State[State[\"BeforeAttributeValue\"] = 11] = \"BeforeAttributeValue\";\n    State[State[\"InAttributeValueDq\"] = 12] = \"InAttributeValueDq\";\n    State[State[\"InAttributeValueSq\"] = 13] = \"InAttributeValueSq\";\n    State[State[\"InAttributeValueNq\"] = 14] = \"InAttributeValueNq\";\n    // Declarations\n    State[State[\"BeforeDeclaration\"] = 15] = \"BeforeDeclaration\";\n    State[State[\"InDeclaration\"] = 16] = \"InDeclaration\";\n    // Processing instructions\n    State[State[\"InProcessingInstruction\"] = 17] = \"InProcessingInstruction\";\n    // Comments & CDATA\n    State[State[\"BeforeComment\"] = 18] = \"BeforeComment\";\n    State[State[\"CDATASequence\"] = 19] = \"CDATASequence\";\n    State[State[\"InSpecialComment\"] = 20] = \"InSpecialComment\";\n    State[State[\"InCommentLike\"] = 21] = \"InCommentLike\";\n    // Special tags\n    State[State[\"BeforeSpecialS\"] = 22] = \"BeforeSpecialS\";\n    State[State[\"SpecialStartSequence\"] = 23] = \"SpecialStartSequence\";\n    State[State[\"InSpecialTag\"] = 24] = \"InSpecialTag\";\n    State[State[\"BeforeEntity\"] = 25] = \"BeforeEntity\";\n    State[State[\"BeforeNumericEntity\"] = 26] = \"BeforeNumericEntity\";\n    State[State[\"InNamedEntity\"] = 27] = \"InNamedEntity\";\n    State[State[\"InNumericEntity\"] = 28] = \"InNumericEntity\";\n    State[State[\"InHexEntity\"] = 29] = \"InHexEntity\";\n})(State || (State = {}));\nfunction isWhitespace(c) {\n    return (c === CharCodes.Space ||\n        c === CharCodes.NewLine ||\n        c === CharCodes.Tab ||\n        c === CharCodes.FormFeed ||\n        c === CharCodes.CarriageReturn);\n}\nfunction isEndOfTagSection(c) {\n    return c === CharCodes.Slash || c === CharCodes.Gt || isWhitespace(c);\n}\nfunction isNumber(c) {\n    return c >= CharCodes.Zero && c <= CharCodes.Nine;\n}\nfunction isASCIIAlpha(c) {\n    return ((c >= CharCodes.LowerA && c <= CharCodes.LowerZ) ||\n        (c >= CharCodes.UpperA && c <= CharCodes.UpperZ));\n}\nfunction isHexDigit(c) {\n    return ((c >= CharCodes.UpperA && c <= CharCodes.UpperF) ||\n        (c >= CharCodes.LowerA && c <= CharCodes.LowerF));\n}\nvar QuoteType;\n(function (QuoteType) {\n    QuoteType[QuoteType[\"NoValue\"] = 0] = \"NoValue\";\n    QuoteType[QuoteType[\"Unquoted\"] = 1] = \"Unquoted\";\n    QuoteType[QuoteType[\"Single\"] = 2] = \"Single\";\n    QuoteType[QuoteType[\"Double\"] = 3] = \"Double\";\n})(QuoteType || (QuoteType = {}));\n/**\n * Sequences used to match longer strings.\n *\n * We don't have `Script`, `Style`, or `Title` here. Instead, we re-use the *End\n * sequences with an increased offset.\n */\nconst Sequences = {\n    Cdata: new Uint8Array([0x43, 0x44, 0x41, 0x54, 0x41, 0x5b]),\n    CdataEnd: new Uint8Array([0x5d, 0x5d, 0x3e]),\n    CommentEnd: new Uint8Array([0x2d, 0x2d, 0x3e]),\n    ScriptEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74]),\n    StyleEnd: new Uint8Array([0x3c, 0x2f, 0x73, 0x74, 0x79, 0x6c, 0x65]),\n    TitleEnd: new Uint8Array([0x3c, 0x2f, 0x74, 0x69, 0x74, 0x6c, 0x65]), // `</title`\n};\nclass Tokenizer {\n    constructor({ xmlMode = false, decodeEntities = true, }, cbs) {\n        this.cbs = cbs;\n        /** The current state the tokenizer is in. */\n        this.state = State.Text;\n        /** The read buffer. */\n        this.buffer = \"\";\n        /** The beginning of the section that is currently being read. */\n        this.sectionStart = 0;\n        /** The index within the buffer that we are currently looking at. */\n        this.index = 0;\n        /** Some behavior, eg. when decoding entities, is done while we are in another state. This keeps track of the other state type. */\n        this.baseState = State.Text;\n        /** For special parsing behavior inside of script and style tags. */\n        this.isSpecial = false;\n        /** Indicates whether the tokenizer has been paused. */\n        this.running = true;\n        /** The offset of the current buffer. */\n        this.offset = 0;\n        this.currentSequence = undefined;\n        this.sequenceIndex = 0;\n        this.trieIndex = 0;\n        this.trieCurrent = 0;\n        /** For named entities, the index of the value. For numeric entities, the code point. */\n        this.entityResult = 0;\n        this.entityExcess = 0;\n        this.xmlMode = xmlMode;\n        this.decodeEntities = decodeEntities;\n        this.entityTrie = xmlMode ? entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.xmlDecodeTree : entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.htmlDecodeTree;\n    }\n    reset() {\n        this.state = State.Text;\n        this.buffer = \"\";\n        this.sectionStart = 0;\n        this.index = 0;\n        this.baseState = State.Text;\n        this.currentSequence = undefined;\n        this.running = true;\n        this.offset = 0;\n    }\n    write(chunk) {\n        this.offset += this.buffer.length;\n        this.buffer = chunk;\n        this.parse();\n    }\n    end() {\n        if (this.running)\n            this.finish();\n    }\n    pause() {\n        this.running = false;\n    }\n    resume() {\n        this.running = true;\n        if (this.index < this.buffer.length + this.offset) {\n            this.parse();\n        }\n    }\n    /**\n     * The current index within all of the written data.\n     */\n    getIndex() {\n        return this.index;\n    }\n    /**\n     * The start of the current section.\n     */\n    getSectionStart() {\n        return this.sectionStart;\n    }\n    stateText(c) {\n        if (c === CharCodes.Lt ||\n            (!this.decodeEntities && this.fastForwardTo(CharCodes.Lt))) {\n            if (this.index > this.sectionStart) {\n                this.cbs.ontext(this.sectionStart, this.index);\n            }\n            this.state = State.BeforeTagName;\n            this.sectionStart = this.index;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateSpecialStartSequence(c) {\n        const isEnd = this.sequenceIndex === this.currentSequence.length;\n        const isMatch = isEnd\n            ? // If we are at the end of the sequence, make sure the tag name has ended\n                isEndOfTagSection(c)\n            : // Otherwise, do a case-insensitive comparison\n                (c | 0x20) === this.currentSequence[this.sequenceIndex];\n        if (!isMatch) {\n            this.isSpecial = false;\n        }\n        else if (!isEnd) {\n            this.sequenceIndex++;\n            return;\n        }\n        this.sequenceIndex = 0;\n        this.state = State.InTagName;\n        this.stateInTagName(c);\n    }\n    /** Look for an end tag. For <title> tags, also decode entities. */\n    stateInSpecialTag(c) {\n        if (this.sequenceIndex === this.currentSequence.length) {\n            if (c === CharCodes.Gt || isWhitespace(c)) {\n                const endOfText = this.index - this.currentSequence.length;\n                if (this.sectionStart < endOfText) {\n                    // Spoof the index so that reported locations match up.\n                    const actualIndex = this.index;\n                    this.index = endOfText;\n                    this.cbs.ontext(this.sectionStart, endOfText);\n                    this.index = actualIndex;\n                }\n                this.isSpecial = false;\n                this.sectionStart = endOfText + 2; // Skip over the `</`\n                this.stateInClosingTagName(c);\n                return; // We are done; skip the rest of the function.\n            }\n            this.sequenceIndex = 0;\n        }\n        if ((c | 0x20) === this.currentSequence[this.sequenceIndex]) {\n            this.sequenceIndex += 1;\n        }\n        else if (this.sequenceIndex === 0) {\n            if (this.currentSequence === Sequences.TitleEnd) {\n                // We have to parse entities in <title> tags.\n                if (this.decodeEntities && c === CharCodes.Amp) {\n                    this.state = State.BeforeEntity;\n                }\n            }\n            else if (this.fastForwardTo(CharCodes.Lt)) {\n                // Outside of <title> tags, we can fast-forward.\n                this.sequenceIndex = 1;\n            }\n        }\n        else {\n            // If we see a `<`, set the sequence index to 1; useful for eg. `<</script>`.\n            this.sequenceIndex = Number(c === CharCodes.Lt);\n        }\n    }\n    stateCDATASequence(c) {\n        if (c === Sequences.Cdata[this.sequenceIndex]) {\n            if (++this.sequenceIndex === Sequences.Cdata.length) {\n                this.state = State.InCommentLike;\n                this.currentSequence = Sequences.CdataEnd;\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n            }\n        }\n        else {\n            this.sequenceIndex = 0;\n            this.state = State.InDeclaration;\n            this.stateInDeclaration(c); // Reconsume the character\n        }\n    }\n    /**\n     * When we wait for one specific character, we can speed things up\n     * by skipping through the buffer until we find it.\n     *\n     * @returns Whether the character was found.\n     */\n    fastForwardTo(c) {\n        while (++this.index < this.buffer.length + this.offset) {\n            if (this.buffer.charCodeAt(this.index - this.offset) === c) {\n                return true;\n            }\n        }\n        /*\n         * We increment the index at the end of the `parse` loop,\n         * so set it to `buffer.length - 1` here.\n         *\n         * TODO: Refactor `parse` to increment index before calling states.\n         */\n        this.index = this.buffer.length + this.offset - 1;\n        return false;\n    }\n    /**\n     * Comments and CDATA end with `-->` and `]]>`.\n     *\n     * Their common qualities are:\n     * - Their end sequences have a distinct character they start with.\n     * - That character is then repeated, so we have to check multiple repeats.\n     * - All characters but the start character of the sequence can be skipped.\n     */\n    stateInCommentLike(c) {\n        if (c === this.currentSequence[this.sequenceIndex]) {\n            if (++this.sequenceIndex === this.currentSequence.length) {\n                if (this.currentSequence === Sequences.CdataEnd) {\n                    this.cbs.oncdata(this.sectionStart, this.index, 2);\n                }\n                else {\n                    this.cbs.oncomment(this.sectionStart, this.index, 2);\n                }\n                this.sequenceIndex = 0;\n                this.sectionStart = this.index + 1;\n                this.state = State.Text;\n            }\n        }\n        else if (this.sequenceIndex === 0) {\n            // Fast-forward to the first character of the sequence\n            if (this.fastForwardTo(this.currentSequence[0])) {\n                this.sequenceIndex = 1;\n            }\n        }\n        else if (c !== this.currentSequence[this.sequenceIndex - 1]) {\n            // Allow long sequences, eg. --->, ]]]>\n            this.sequenceIndex = 0;\n        }\n    }\n    /**\n     * HTML only allows ASCII alpha characters (a-z and A-Z) at the beginning of a tag name.\n     *\n     * XML allows a lot more characters here (@see https://www.w3.org/TR/REC-xml/#NT-NameStartChar).\n     * We allow anything that wouldn't end the tag.\n     */\n    isTagStartChar(c) {\n        return this.xmlMode ? !isEndOfTagSection(c) : isASCIIAlpha(c);\n    }\n    startSpecial(sequence, offset) {\n        this.isSpecial = true;\n        this.currentSequence = sequence;\n        this.sequenceIndex = offset;\n        this.state = State.SpecialStartSequence;\n    }\n    stateBeforeTagName(c) {\n        if (c === CharCodes.ExclamationMark) {\n            this.state = State.BeforeDeclaration;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Questionmark) {\n            this.state = State.InProcessingInstruction;\n            this.sectionStart = this.index + 1;\n        }\n        else if (this.isTagStartChar(c)) {\n            const lower = c | 0x20;\n            this.sectionStart = this.index;\n            if (!this.xmlMode && lower === Sequences.TitleEnd[2]) {\n                this.startSpecial(Sequences.TitleEnd, 3);\n            }\n            else {\n                this.state =\n                    !this.xmlMode && lower === Sequences.ScriptEnd[2]\n                        ? State.BeforeSpecialS\n                        : State.InTagName;\n            }\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.BeforeClosingTagName;\n        }\n        else {\n            this.state = State.Text;\n            this.stateText(c);\n        }\n    }\n    stateInTagName(c) {\n        if (isEndOfTagSection(c)) {\n            this.cbs.onopentagname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateBeforeClosingTagName(c) {\n        if (isWhitespace(c)) {\n            // Ignore\n        }\n        else if (c === CharCodes.Gt) {\n            this.state = State.Text;\n        }\n        else {\n            this.state = this.isTagStartChar(c)\n                ? State.InClosingTagName\n                : State.InSpecialComment;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInClosingTagName(c) {\n        if (c === CharCodes.Gt || isWhitespace(c)) {\n            this.cbs.onclosetag(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterClosingTagName;\n            this.stateAfterClosingTagName(c);\n        }\n    }\n    stateAfterClosingTagName(c) {\n        // Skip everything until \">\"\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeAttributeName(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onopentagend(this.index);\n            if (this.isSpecial) {\n                this.state = State.InSpecialTag;\n                this.sequenceIndex = 0;\n            }\n            else {\n                this.state = State.Text;\n            }\n            this.baseState = this.state;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.Slash) {\n            this.state = State.InSelfClosingTag;\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateInSelfClosingTag(c) {\n        if (c === CharCodes.Gt) {\n            this.cbs.onselfclosingtag(this.index);\n            this.state = State.Text;\n            this.baseState = State.Text;\n            this.sectionStart = this.index + 1;\n            this.isSpecial = false; // Reset special state, in case of self-closing special tags\n        }\n        else if (!isWhitespace(c)) {\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n    }\n    stateInAttributeName(c) {\n        if (c === CharCodes.Eq || isEndOfTagSection(c)) {\n            this.cbs.onattribname(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (c === CharCodes.Eq) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else if (c === CharCodes.Slash || c === CharCodes.Gt) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (!isWhitespace(c)) {\n            this.cbs.onattribend(QuoteType.NoValue, this.index);\n            this.state = State.InAttributeName;\n            this.sectionStart = this.index;\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (c === CharCodes.DoubleQuote) {\n            this.state = State.InAttributeValueDq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (c === CharCodes.SingleQuote) {\n            this.state = State.InAttributeValueSq;\n            this.sectionStart = this.index + 1;\n        }\n        else if (!isWhitespace(c)) {\n            this.sectionStart = this.index;\n            this.state = State.InAttributeValueNq;\n            this.stateInAttributeValueNoQuotes(c); // Reconsume token\n        }\n    }\n    handleInAttributeValue(c, quote) {\n        if (c === quote ||\n            (!this.decodeEntities && this.fastForwardTo(quote))) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(quote === CharCodes.DoubleQuote\n                ? QuoteType.Double\n                : QuoteType.Single, this.index);\n            this.state = State.BeforeAttributeName;\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateInAttributeValueDoubleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.DoubleQuote);\n    }\n    stateInAttributeValueSingleQuotes(c) {\n        this.handleInAttributeValue(c, CharCodes.SingleQuote);\n    }\n    stateInAttributeValueNoQuotes(c) {\n        if (isWhitespace(c) || c === CharCodes.Gt) {\n            this.cbs.onattribdata(this.sectionStart, this.index);\n            this.sectionStart = -1;\n            this.cbs.onattribend(QuoteType.Unquoted, this.index);\n            this.state = State.BeforeAttributeName;\n            this.stateBeforeAttributeName(c);\n        }\n        else if (this.decodeEntities && c === CharCodes.Amp) {\n            this.baseState = this.state;\n            this.state = State.BeforeEntity;\n        }\n    }\n    stateBeforeDeclaration(c) {\n        if (c === CharCodes.OpeningSquareBracket) {\n            this.state = State.CDATASequence;\n            this.sequenceIndex = 0;\n        }\n        else {\n            this.state =\n                c === CharCodes.Dash\n                    ? State.BeforeComment\n                    : State.InDeclaration;\n        }\n    }\n    stateInDeclaration(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.ondeclaration(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateInProcessingInstruction(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.onprocessinginstruction(this.sectionStart, this.index);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeComment(c) {\n        if (c === CharCodes.Dash) {\n            this.state = State.InCommentLike;\n            this.currentSequence = Sequences.CommentEnd;\n            // Allow short comments (eg. <!-->)\n            this.sequenceIndex = 2;\n            this.sectionStart = this.index + 1;\n        }\n        else {\n            this.state = State.InDeclaration;\n        }\n    }\n    stateInSpecialComment(c) {\n        if (c === CharCodes.Gt || this.fastForwardTo(CharCodes.Gt)) {\n            this.cbs.oncomment(this.sectionStart, this.index, 0);\n            this.state = State.Text;\n            this.sectionStart = this.index + 1;\n        }\n    }\n    stateBeforeSpecialS(c) {\n        const lower = c | 0x20;\n        if (lower === Sequences.ScriptEnd[3]) {\n            this.startSpecial(Sequences.ScriptEnd, 4);\n        }\n        else if (lower === Sequences.StyleEnd[3]) {\n            this.startSpecial(Sequences.StyleEnd, 4);\n        }\n        else {\n            this.state = State.InTagName;\n            this.stateInTagName(c); // Consume the token again\n        }\n    }\n    stateBeforeEntity(c) {\n        // Start excess with 1 to include the '&'\n        this.entityExcess = 1;\n        this.entityResult = 0;\n        if (c === CharCodes.Number) {\n            this.state = State.BeforeNumericEntity;\n        }\n        else if (c === CharCodes.Amp) {\n            // We have two `&` characters in a row. Stay in the current state.\n        }\n        else {\n            this.trieIndex = 0;\n            this.trieCurrent = this.entityTrie[0];\n            this.state = State.InNamedEntity;\n            this.stateInNamedEntity(c);\n        }\n    }\n    stateInNamedEntity(c) {\n        this.entityExcess += 1;\n        this.trieIndex = (0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.determineBranch)(this.entityTrie, this.trieCurrent, this.trieIndex + 1, c);\n        if (this.trieIndex < 0) {\n            this.emitNamedEntity();\n            this.index--;\n            return;\n        }\n        this.trieCurrent = this.entityTrie[this.trieIndex];\n        const masked = this.trieCurrent & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH;\n        // If the branch is a value, store it and continue\n        if (masked) {\n            // The mask is the number of bytes of the value, including the current byte.\n            const valueLength = (masked >> 14) - 1;\n            // If we have a legacy entity while parsing strictly, just skip the number of bytes\n            if (!this.allowLegacyEntity() && c !== CharCodes.Semi) {\n                this.trieIndex += valueLength;\n            }\n            else {\n                // Add 1 as we have already incremented the excess\n                const entityStart = this.index - this.entityExcess + 1;\n                if (entityStart > this.sectionStart) {\n                    this.emitPartial(this.sectionStart, entityStart);\n                }\n                // If this is a surrogate pair, consume the next two bytes\n                this.entityResult = this.trieIndex;\n                this.trieIndex += valueLength;\n                this.entityExcess = 0;\n                this.sectionStart = this.index + 1;\n                if (valueLength === 0) {\n                    this.emitNamedEntity();\n                }\n            }\n        }\n    }\n    emitNamedEntity() {\n        this.state = this.baseState;\n        if (this.entityResult === 0) {\n            return;\n        }\n        const valueLength = (this.entityTrie[this.entityResult] & entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH) >>\n            14;\n        switch (valueLength) {\n            case 1: {\n                this.emitCodePoint(this.entityTrie[this.entityResult] &\n                    ~entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.BinTrieFlags.VALUE_LENGTH);\n                break;\n            }\n            case 2: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                break;\n            }\n            case 3: {\n                this.emitCodePoint(this.entityTrie[this.entityResult + 1]);\n                this.emitCodePoint(this.entityTrie[this.entityResult + 2]);\n            }\n        }\n    }\n    stateBeforeNumericEntity(c) {\n        if ((c | 0x20) === CharCodes.LowerX) {\n            this.entityExcess++;\n            this.state = State.InHexEntity;\n        }\n        else {\n            this.state = State.InNumericEntity;\n            this.stateInNumericEntity(c);\n        }\n    }\n    emitNumericEntity(strict) {\n        const entityStart = this.index - this.entityExcess - 1;\n        const numberStart = entityStart + 2 + Number(this.state === State.InHexEntity);\n        if (numberStart !== this.index) {\n            // Emit leading data if any\n            if (entityStart > this.sectionStart) {\n                this.emitPartial(this.sectionStart, entityStart);\n            }\n            this.sectionStart = this.index + Number(strict);\n            this.emitCodePoint((0,entities_lib_decode_js__WEBPACK_IMPORTED_MODULE_0__.replaceCodePoint)(this.entityResult));\n        }\n        this.state = this.baseState;\n    }\n    stateInNumericEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 10 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    stateInHexEntity(c) {\n        if (c === CharCodes.Semi) {\n            this.emitNumericEntity(true);\n        }\n        else if (isNumber(c)) {\n            this.entityResult = this.entityResult * 16 + (c - CharCodes.Zero);\n            this.entityExcess++;\n        }\n        else if (isHexDigit(c)) {\n            this.entityResult =\n                this.entityResult * 16 + ((c | 0x20) - CharCodes.LowerA + 10);\n            this.entityExcess++;\n        }\n        else {\n            if (this.allowLegacyEntity()) {\n                this.emitNumericEntity(false);\n            }\n            else {\n                this.state = this.baseState;\n            }\n            this.index--;\n        }\n    }\n    allowLegacyEntity() {\n        return (!this.xmlMode &&\n            (this.baseState === State.Text ||\n                this.baseState === State.InSpecialTag));\n    }\n    /**\n     * Remove data that has already been consumed from the buffer.\n     */\n    cleanup() {\n        // If we are inside of text or attributes, emit what we already have.\n        if (this.running && this.sectionStart !== this.index) {\n            if (this.state === State.Text ||\n                (this.state === State.InSpecialTag && this.sequenceIndex === 0)) {\n                this.cbs.ontext(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n            else if (this.state === State.InAttributeValueDq ||\n                this.state === State.InAttributeValueSq ||\n                this.state === State.InAttributeValueNq) {\n                this.cbs.onattribdata(this.sectionStart, this.index);\n                this.sectionStart = this.index;\n            }\n        }\n    }\n    shouldContinue() {\n        return this.index < this.buffer.length + this.offset && this.running;\n    }\n    /**\n     * Iterates through the buffer, calling the function corresponding to the current state.\n     *\n     * States that are more likely to be hit are higher up, as a performance improvement.\n     */\n    parse() {\n        while (this.shouldContinue()) {\n            const c = this.buffer.charCodeAt(this.index - this.offset);\n            switch (this.state) {\n                case State.Text: {\n                    this.stateText(c);\n                    break;\n                }\n                case State.SpecialStartSequence: {\n                    this.stateSpecialStartSequence(c);\n                    break;\n                }\n                case State.InSpecialTag: {\n                    this.stateInSpecialTag(c);\n                    break;\n                }\n                case State.CDATASequence: {\n                    this.stateCDATASequence(c);\n                    break;\n                }\n                case State.InAttributeValueDq: {\n                    this.stateInAttributeValueDoubleQuotes(c);\n                    break;\n                }\n                case State.InAttributeName: {\n                    this.stateInAttributeName(c);\n                    break;\n                }\n                case State.InCommentLike: {\n                    this.stateInCommentLike(c);\n                    break;\n                }\n                case State.InSpecialComment: {\n                    this.stateInSpecialComment(c);\n                    break;\n                }\n                case State.BeforeAttributeName: {\n                    this.stateBeforeAttributeName(c);\n                    break;\n                }\n                case State.InTagName: {\n                    this.stateInTagName(c);\n                    break;\n                }\n                case State.InClosingTagName: {\n                    this.stateInClosingTagName(c);\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.InAttributeValueSq: {\n                    this.stateInAttributeValueSingleQuotes(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.BeforeClosingTagName: {\n                    this.stateBeforeClosingTagName(c);\n                    break;\n                }\n                case State.AfterClosingTagName: {\n                    this.stateAfterClosingTagName(c);\n                    break;\n                }\n                case State.BeforeSpecialS: {\n                    this.stateBeforeSpecialS(c);\n                    break;\n                }\n                case State.InAttributeValueNq: {\n                    this.stateInAttributeValueNoQuotes(c);\n                    break;\n                }\n                case State.InSelfClosingTag: {\n                    this.stateInSelfClosingTag(c);\n                    break;\n                }\n                case State.InDeclaration: {\n                    this.stateInDeclaration(c);\n                    break;\n                }\n                case State.BeforeDeclaration: {\n                    this.stateBeforeDeclaration(c);\n                    break;\n                }\n                case State.BeforeComment: {\n                    this.stateBeforeComment(c);\n                    break;\n                }\n                case State.InProcessingInstruction: {\n                    this.stateInProcessingInstruction(c);\n                    break;\n                }\n                case State.InNamedEntity: {\n                    this.stateInNamedEntity(c);\n                    break;\n                }\n                case State.BeforeEntity: {\n                    this.stateBeforeEntity(c);\n                    break;\n                }\n                case State.InHexEntity: {\n                    this.stateInHexEntity(c);\n                    break;\n                }\n                case State.InNumericEntity: {\n                    this.stateInNumericEntity(c);\n                    break;\n                }\n                default: {\n                    // `this._state === State.BeforeNumericEntity`\n                    this.stateBeforeNumericEntity(c);\n                }\n            }\n            this.index++;\n        }\n        this.cleanup();\n    }\n    finish() {\n        if (this.state === State.InNamedEntity) {\n            this.emitNamedEntity();\n        }\n        // If there is remaining data, emit it in a reasonable way\n        if (this.sectionStart < this.index) {\n            this.handleTrailingData();\n        }\n        this.cbs.onend();\n    }\n    /** Handle any trailing data. */\n    handleTrailingData() {\n        const endIndex = this.buffer.length + this.offset;\n        if (this.state === State.InCommentLike) {\n            if (this.currentSequence === Sequences.CdataEnd) {\n                this.cbs.oncdata(this.sectionStart, endIndex, 0);\n            }\n            else {\n                this.cbs.oncomment(this.sectionStart, endIndex, 0);\n            }\n        }\n        else if (this.state === State.InNumericEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InHexEntity &&\n            this.allowLegacyEntity()) {\n            this.emitNumericEntity(false);\n            // All trailing data will have been consumed\n        }\n        else if (this.state === State.InTagName ||\n            this.state === State.BeforeAttributeName ||\n            this.state === State.BeforeAttributeValue ||\n            this.state === State.AfterAttributeName ||\n            this.state === State.InAttributeName ||\n            this.state === State.InAttributeValueSq ||\n            this.state === State.InAttributeValueDq ||\n            this.state === State.InAttributeValueNq ||\n            this.state === State.InClosingTagName) {\n            /*\n             * If we are currently in an opening or closing tag, us not calling the\n             * respective callback signals that the tag should be ignored.\n             */\n        }\n        else {\n            this.cbs.ontext(this.sectionStart, endIndex);\n        }\n    }\n    emitPartial(start, endIndex) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribdata(start, endIndex);\n        }\n        else {\n            this.cbs.ontext(start, endIndex);\n        }\n    }\n    emitCodePoint(cp) {\n        if (this.baseState !== State.Text &&\n            this.baseState !== State.InSpecialTag) {\n            this.cbs.onattribentity(cp);\n        }\n        else {\n            this.cbs.ontextentity(cp);\n        }\n    }\n}\n//# sourceMappingURL=Tokenizer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Tokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/index.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomHandler: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler),\n/* harmony export */   DomUtils: () => (/* reexport module object */ domutils__WEBPACK_IMPORTED_MODULE_4__),\n/* harmony export */   ElementType: () => (/* reexport module object */ domelementtype__WEBPACK_IMPORTED_MODULE_3__),\n/* harmony export */   Parser: () => (/* reexport safe */ _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser),\n/* harmony export */   Tokenizer: () => (/* reexport safe */ _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   createDomStream: () => (/* binding */ createDomStream),\n/* harmony export */   getFeed: () => (/* reexport safe */ domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed),\n/* harmony export */   parseDOM: () => (/* binding */ parseDOM),\n/* harmony export */   parseDocument: () => (/* binding */ parseDocument),\n/* harmony export */   parseFeed: () => (/* binding */ parseFeed)\n/* harmony export */ });\n/* harmony import */ var _Parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Parser.js */ \"(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Parser.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! domhandler */ \"(rsc)/./node_modules/.pnpm/domhandler@5.0.3/node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _Tokenizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Tokenizer.js */ \"(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Tokenizer.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/.pnpm/domelementtype@2.3.0/node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var domutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! domutils */ \"(rsc)/./node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/index.js\");\n\n\n\n\n// Helper methods\n/**\n * Parses the data, returns the resulting document.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n */\nfunction parseDocument(data, options) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(undefined, options);\n    new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options).end(data);\n    return handler.root;\n}\n/**\n * Parses data, returns an array of the root nodes.\n *\n * Note that the root nodes still have a `Document` node as their parent.\n * Use `parseDocument` to get the `Document` node instead.\n *\n * @param data The data that should be parsed.\n * @param options Optional options for the parser and DOM builder.\n * @deprecated Use `parseDocument` instead.\n */\nfunction parseDOM(data, options) {\n    return parseDocument(data, options).children;\n}\n/**\n * Creates a parser instance, with an attached DOM handler.\n *\n * @param callback A callback that will be called once parsing has been completed.\n * @param options Optional options for the parser and DOM builder.\n * @param elementCallback An optional callback that will be called every time a tag has been completed inside of the DOM.\n */\nfunction createDomStream(callback, options, elementCallback) {\n    const handler = new domhandler__WEBPACK_IMPORTED_MODULE_1__.DomHandler(callback, options, elementCallback);\n    return new _Parser_js__WEBPACK_IMPORTED_MODULE_0__.Parser(handler, options);\n}\n\n/*\n * All of the following exports exist for backwards-compatibility.\n * They should probably be removed eventually.\n */\n\n\n\nconst parseFeedDefaultOptions = { xmlMode: true };\n/**\n * Parse a feed.\n *\n * @param feed The feed that should be parsed, as a string.\n * @param options Optionally, options for parsing. When using this, you should set `xmlMode` to `true`.\n */\nfunction parseFeed(feed, options = parseFeedDefaultOptions) {\n    return (0,domutils__WEBPACK_IMPORTED_MODULE_4__.getFeed)(parseDOM(feed, options));\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/index.js\n");

/***/ })

};
;