"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/selderee@0.11.0";
exports.ids = ["vendor-chunks/selderee@0.11.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/selderee@0.11.0/node_modules/selderee/lib/selderee.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/selderee@0.11.0/node_modules/selderee/lib/selderee.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ast: () => (/* binding */ Ast),\n/* harmony export */   DecisionTree: () => (/* binding */ DecisionTree),\n/* harmony export */   Picker: () => (/* binding */ Picker),\n/* harmony export */   Treeify: () => (/* binding */ TreeifyBuilder),\n/* harmony export */   Types: () => (/* binding */ Types)\n/* harmony export */ });\n/* harmony import */ var parseley__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parseley */ \"(rsc)/./node_modules/.pnpm/parseley@0.12.1/node_modules/parseley/lib/parseley.mjs\");\n\n\n\nvar Ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar Types = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst treeify = (nodes) => '▽\\n' + treeifyArray(nodes, thinLines);\nconst thinLines = [['├─', '│ '], ['└─', '  ']];\nconst heavyLines = [['┠─', '┃ '], ['┖─', '  ']];\nconst doubleLines = [['╟─', '║ '], ['╙─', '  ']];\nfunction treeifyArray(nodes, tpl = heavyLines) {\n    return prefixItems(tpl, nodes.map(n => treeifyNode(n)));\n}\nfunction treeifyNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const vctr = node.valueContainer;\n            return `◁ #${vctr.index} ${JSON.stringify(vctr.specificity)} ${vctr.value}`;\n        }\n        case 'tagName':\n            return `◻ Tag name\\n${treeifyArray(node.variants, doubleLines)}`;\n        case 'attrValue':\n            return `▣ Attr value: ${node.name}\\n${treeifyArray(node.matchers, doubleLines)}`;\n        case 'attrPresence':\n            return `◨ Attr presence: ${node.name}\\n${treeifyArray(node.cont)}`;\n        case 'pushElement':\n            return `◉ Push element: ${node.combinator}\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'popElement':\n            return `◌ Pop element\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'variant':\n            return `◇ = ${node.value}\\n${treeifyArray(node.cont)}`;\n        case 'matcher':\n            return `◈ ${node.matcher} \"${node.value}\"${node.modifier || ''}\\n${treeifyArray(node.cont)}`;\n    }\n}\nfunction prefixItems(tpl, items) {\n    return items\n        .map((item, i, { length }) => prefixItem(tpl, item, i === length - 1))\n        .join('\\n');\n}\nfunction prefixItem(tpl, item, tail = true) {\n    const tpl1 = tpl[tail ? 1 : 0];\n    return tpl1[0] + item.split('\\n').join('\\n' + tpl1[1]);\n}\n\nvar TreeifyBuilder = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    treeify: treeify\n});\n\nclass DecisionTree {\n    constructor(input) {\n        this.branches = weave(toAstTerminalPairs(input));\n    }\n    build(builder) {\n        return builder(this.branches);\n    }\n}\nfunction toAstTerminalPairs(array) {\n    const len = array.length;\n    const results = new Array(len);\n    for (let i = 0; i < len; i++) {\n        const [selectorString, val] = array[i];\n        const ast = preprocess(parseley__WEBPACK_IMPORTED_MODULE_0__.parse1(selectorString));\n        results[i] = {\n            ast: ast,\n            terminal: {\n                type: 'terminal',\n                valueContainer: { index: i, value: val, specificity: ast.specificity }\n            }\n        };\n    }\n    return results;\n}\nfunction preprocess(ast) {\n    reduceSelectorVariants(ast);\n    parseley__WEBPACK_IMPORTED_MODULE_0__.normalize(ast);\n    return ast;\n}\nfunction reduceSelectorVariants(ast) {\n    const newList = [];\n    ast.list.forEach(sel => {\n        switch (sel.type) {\n            case 'class':\n                newList.push({\n                    matcher: '~=',\n                    modifier: null,\n                    name: 'class',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'id':\n                newList.push({\n                    matcher: '=',\n                    modifier: null,\n                    name: 'id',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'combinator':\n                reduceSelectorVariants(sel.left);\n                newList.push(sel);\n                break;\n            case 'universal':\n                break;\n            default:\n                newList.push(sel);\n                break;\n        }\n    });\n    ast.list = newList;\n}\nfunction weave(items) {\n    const branches = [];\n    while (items.length) {\n        const topKind = findTopKey(items, (sel) => true, getSelectorKind);\n        const { matches, nonmatches, empty } = breakByKind(items, topKind);\n        items = nonmatches;\n        if (matches.length) {\n            branches.push(branchOfKind(topKind, matches));\n        }\n        if (empty.length) {\n            branches.push(...terminate(empty));\n        }\n    }\n    return branches;\n}\nfunction terminate(items) {\n    const results = [];\n    for (const item of items) {\n        const terminal = item.terminal;\n        if (terminal.type === 'terminal') {\n            results.push(terminal);\n        }\n        else {\n            const { matches, rest } = partition(terminal.cont, (node) => node.type === 'terminal');\n            matches.forEach((node) => results.push(node));\n            if (rest.length) {\n                terminal.cont = rest;\n                results.push(terminal);\n            }\n        }\n    }\n    return results;\n}\nfunction breakByKind(items, selectedKind) {\n    const matches = [];\n    const nonmatches = [];\n    const empty = [];\n    for (const item of items) {\n        const simpsels = item.ast.list;\n        if (simpsels.length) {\n            const isMatch = simpsels.some(node => getSelectorKind(node) === selectedKind);\n            (isMatch ? matches : nonmatches).push(item);\n        }\n        else {\n            empty.push(item);\n        }\n    }\n    return { matches, nonmatches, empty };\n}\nfunction getSelectorKind(sel) {\n    switch (sel.type) {\n        case 'attrPresence':\n            return `attrPresence ${sel.name}`;\n        case 'attrValue':\n            return `attrValue ${sel.name}`;\n        case 'combinator':\n            return `combinator ${sel.combinator}`;\n        default:\n            return sel.type;\n    }\n}\nfunction branchOfKind(kind, items) {\n    if (kind === 'tag') {\n        return tagNameBranch(items);\n    }\n    if (kind.startsWith('attrValue ')) {\n        return attrValueBranch(kind.substring(10), items);\n    }\n    if (kind.startsWith('attrPresence ')) {\n        return attrPresenceBranch(kind.substring(13), items);\n    }\n    if (kind === 'combinator >') {\n        return combinatorBranch('>', items);\n    }\n    if (kind === 'combinator +') {\n        return combinatorBranch('+', items);\n    }\n    throw new Error(`Unsupported selector kind: ${kind}`);\n}\nfunction tagNameBranch(items) {\n    const groups = spliceAndGroup(items, (x) => x.type === 'tag', (x) => x.name);\n    const variants = Object.entries(groups).map(([name, group]) => ({\n        type: 'variant',\n        value: name,\n        cont: weave(group.items)\n    }));\n    return {\n        type: 'tagName',\n        variants: variants\n    };\n}\nfunction attrPresenceBranch(name, items) {\n    for (const item of items) {\n        spliceSimpleSelector(item, (x) => (x.type === 'attrPresence') && (x.name === name));\n    }\n    return {\n        type: 'attrPresence',\n        name: name,\n        cont: weave(items)\n    };\n}\nfunction attrValueBranch(name, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'attrValue') && (x.name === name), (x) => `${x.matcher} ${x.modifier || ''} ${x.value}`);\n    const matchers = [];\n    for (const group of Object.values(groups)) {\n        const sel = group.oneSimpleSelector;\n        const predicate = getAttrPredicate(sel);\n        const continuation = weave(group.items);\n        matchers.push({\n            type: 'matcher',\n            matcher: sel.matcher,\n            modifier: sel.modifier,\n            value: sel.value,\n            predicate: predicate,\n            cont: continuation\n        });\n    }\n    return {\n        type: 'attrValue',\n        name: name,\n        matchers: matchers\n    };\n}\nfunction getAttrPredicate(sel) {\n    if (sel.modifier === 'i') {\n        const expected = sel.value.toLowerCase();\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual.toLowerCase();\n            case '~=':\n                return (actual) => actual.toLowerCase().split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.toLowerCase().startsWith(expected);\n            case '$=':\n                return (actual) => actual.toLowerCase().endsWith(expected);\n            case '*=':\n                return (actual) => actual.toLowerCase().includes(expected);\n            case '|=':\n                return (actual) => {\n                    const lower = actual.toLowerCase();\n                    return (expected === lower) || (lower.startsWith(expected) && lower[expected.length] === '-');\n                };\n        }\n    }\n    else {\n        const expected = sel.value;\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual;\n            case '~=':\n                return (actual) => actual.split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.startsWith(expected);\n            case '$=':\n                return (actual) => actual.endsWith(expected);\n            case '*=':\n                return (actual) => actual.includes(expected);\n            case '|=':\n                return (actual) => (expected === actual) || (actual.startsWith(expected) && actual[expected.length] === '-');\n        }\n    }\n}\nfunction combinatorBranch(combinator, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'combinator') && (x.combinator === combinator), (x) => parseley__WEBPACK_IMPORTED_MODULE_0__.serialize(x.left));\n    const leftItems = [];\n    for (const group of Object.values(groups)) {\n        const rightCont = weave(group.items);\n        const leftAst = group.oneSimpleSelector.left;\n        leftItems.push({\n            ast: leftAst,\n            terminal: { type: 'popElement', cont: rightCont }\n        });\n    }\n    return {\n        type: 'pushElement',\n        combinator: combinator,\n        cont: weave(leftItems)\n    };\n}\nfunction spliceAndGroup(items, predicate, keyCallback) {\n    const groups = {};\n    while (items.length) {\n        const bestKey = findTopKey(items, predicate, keyCallback);\n        const bestKeyPredicate = (sel) => predicate(sel) && keyCallback(sel) === bestKey;\n        const hasBestKeyPredicate = (item) => item.ast.list.some(bestKeyPredicate);\n        const { matches, rest } = partition1(items, hasBestKeyPredicate);\n        let oneSimpleSelector = null;\n        for (const item of matches) {\n            const splicedNode = spliceSimpleSelector(item, bestKeyPredicate);\n            if (!oneSimpleSelector) {\n                oneSimpleSelector = splicedNode;\n            }\n        }\n        if (oneSimpleSelector == null) {\n            throw new Error('No simple selector is found.');\n        }\n        groups[bestKey] = { oneSimpleSelector: oneSimpleSelector, items: matches };\n        items = rest;\n    }\n    return groups;\n}\nfunction spliceSimpleSelector(item, predicate) {\n    const simpsels = item.ast.list;\n    const matches = new Array(simpsels.length);\n    let firstIndex = -1;\n    for (let i = simpsels.length; i-- > 0;) {\n        if (predicate(simpsels[i])) {\n            matches[i] = true;\n            firstIndex = i;\n        }\n    }\n    if (firstIndex == -1) {\n        throw new Error(`Couldn't find the required simple selector.`);\n    }\n    const result = simpsels[firstIndex];\n    item.ast.list = simpsels.filter((sel, i) => !matches[i]);\n    return result;\n}\nfunction findTopKey(items, predicate, keyCallback) {\n    const candidates = {};\n    for (const item of items) {\n        const candidates1 = {};\n        for (const node of item.ast.list.filter(predicate)) {\n            candidates1[keyCallback(node)] = true;\n        }\n        for (const key of Object.keys(candidates1)) {\n            if (candidates[key]) {\n                candidates[key]++;\n            }\n            else {\n                candidates[key] = 1;\n            }\n        }\n    }\n    let topKind = '';\n    let topCounter = 0;\n    for (const entry of Object.entries(candidates)) {\n        if (entry[1] > topCounter) {\n            topKind = entry[0];\n            topCounter = entry[1];\n        }\n    }\n    return topKind;\n}\nfunction partition(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\nfunction partition1(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\n\nclass Picker {\n    constructor(f) {\n        this.f = f;\n    }\n    pickAll(el) {\n        return this.f(el);\n    }\n    pick1(el, preferFirst = false) {\n        const results = this.f(el);\n        const len = results.length;\n        if (len === 0) {\n            return null;\n        }\n        if (len === 1) {\n            return results[0].value;\n        }\n        const comparator = (preferFirst)\n            ? comparatorPreferFirst\n            : comparatorPreferLast;\n        let result = results[0];\n        for (let i = 1; i < len; i++) {\n            const next = results[i];\n            if (comparator(result, next)) {\n                result = next;\n            }\n        }\n        return result.value;\n    }\n}\nfunction comparatorPreferFirst(acc, next) {\n    const diff = (0,parseley__WEBPACK_IMPORTED_MODULE_0__.compareSpecificity)(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index < acc.index);\n}\nfunction comparatorPreferLast(acc, next) {\n    const diff = (0,parseley__WEBPACK_IMPORTED_MODULE_0__.compareSpecificity)(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index > acc.index);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/selderee@0.11.0/node_modules/selderee/lib/selderee.mjs\n");

/***/ })

};
;