"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-to-text@9.0.5";
exports.ids = ["vendor-chunks/html-to-text@9.0.5"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/html-to-text@9.0.5/node_modules/html-to-text/lib/html-to-text.mjs":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/html-to-text@9.0.5/node_modules/html-to-text/lib/html-to-text.mjs ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compile: () => (/* binding */ compile),\n/* harmony export */   convert: () => (/* binding */ convert),\n/* harmony export */   htmlToText: () => (/* binding */ convert)\n/* harmony export */ });\n/* harmony import */ var _selderee_plugin_htmlparser2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @selderee/plugin-htmlparser2 */ \"(rsc)/./node_modules/.pnpm/@selderee+plugin-htmlparser2@0.11.0/node_modules/@selderee/plugin-htmlparser2/lib/hp2-builder.mjs\");\n/* harmony import */ var htmlparser2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! htmlparser2 */ \"(rsc)/./node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/index.js\");\n/* harmony import */ var selderee__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! selderee */ \"(rsc)/./node_modules/.pnpm/selderee@0.11.0/node_modules/selderee/lib/selderee.mjs\");\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! deepmerge */ \"(rsc)/./node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js\");\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(deepmerge__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dom_serializer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dom-serializer */ \"(rsc)/./node_modules/.pnpm/dom-serializer@2.0.0/node_modules/dom-serializer/lib/esm/index.js\");\n\n\n\n\n\n\n/**\n * Make a recursive function that will only run to a given depth\n * and switches to an alternative function at that depth. \\\n * No limitation if `n` is `undefined` (Just wraps `f` in that case).\n *\n * @param   { number | undefined } n   Allowed depth of recursion. `undefined` for no limitation.\n * @param   { Function }           f   Function that accepts recursive callback as the first argument.\n * @param   { Function }           [g] Function to run instead, when maximum depth was reached. Do nothing by default.\n * @returns { Function }\n */\nfunction limitedDepthRecursive (n, f, g = () => undefined) {\n  if (n === undefined) {\n    const f1 = function (...args) { return f(f1, ...args); };\n    return f1;\n  }\n  if (n >= 0) {\n    return function (...args) { return f(limitedDepthRecursive(n - 1, f, g), ...args); };\n  }\n  return g;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from each side.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacter (str, char) {\n  let start = 0;\n  let end = str.length;\n  while (start < end && str[start] === char) { ++start; }\n  while (end > start && str[end - 1] === char) { --end; }\n  return (start > 0 || end < str.length)\n    ? str.substring(start, end)\n    : str;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from the end only.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacterEnd (str, char) {\n  let end = str.length;\n  while (end > 0 && str[end - 1] === char) { --end; }\n  return (end < str.length)\n    ? str.substring(0, end)\n    : str;\n}\n\n/**\n * Return a new string will all characters replaced with unicode escape sequences.\n * This extreme kind of escaping can used to be safely compose regular expressions.\n *\n * @param { string } str A string to escape.\n * @returns { string } A string of unicode escape sequences.\n */\nfunction unicodeEscape (str) {\n  return str.replace(/[\\s\\S]/g, c => '\\\\u' + c.charCodeAt().toString(16).padStart(4, '0'));\n}\n\n/**\n * Deduplicate an array by a given key callback.\n * Item properties are merged recursively and with the preference for last defined values.\n * Of items with the same key, merged item takes the place of the last item,\n * others are omitted.\n *\n * @param { any[] } items An array to deduplicate.\n * @param { (x: any) => string } getKey Callback to get a value that distinguishes unique items.\n * @returns { any[] }\n */\nfunction mergeDuplicatesPreferLast (items, getKey) {\n  const map = new Map();\n  for (let i = items.length; i-- > 0;) {\n    const item = items[i];\n    const key = getKey(item);\n    map.set(\n      key,\n      (map.has(key))\n        ? deepmerge__WEBPACK_IMPORTED_MODULE_2___default()(item, map.get(key), { arrayMerge: overwriteMerge$1 })\n        : item\n    );\n  }\n  return [...map.values()].reverse();\n}\n\nconst overwriteMerge$1 = (acc, src, options) => [...src];\n\n/**\n * Get a nested property from an object.\n *\n * @param   { object }   obj  The object to query for the value.\n * @param   { string[] } path The path to the property.\n * @returns { any }\n */\nfunction get (obj, path) {\n  for (const key of path) {\n    if (!obj) { return undefined; }\n    obj = obj[key];\n  }\n  return obj;\n}\n\n/**\n * Convert a number into alphabetic sequence representation (Sequence without zeroes).\n *\n * For example: `a, ..., z, aa, ..., zz, aaa, ...`.\n *\n * @param   { number } num              Number to convert. Must be >= 1.\n * @param   { string } [baseChar = 'a'] Character for 1 in the sequence.\n * @param   { number } [base = 26]      Number of characters in the sequence.\n * @returns { string }\n */\nfunction numberToLetterSequence (num, baseChar = 'a', base = 26) {\n  const digits = [];\n  do {\n    num -= 1;\n    digits.push(num % base);\n    num = (num / base) >> 0; // quick `floor`\n  } while (num > 0);\n  const baseCode = baseChar.charCodeAt(0);\n  return digits\n    .reverse()\n    .map(n => String.fromCharCode(baseCode + n))\n    .join('');\n}\n\nconst I = ['I', 'X', 'C', 'M'];\nconst V = ['V', 'L', 'D'];\n\n/**\n * Convert a number to it's Roman representation. No large numbers extension.\n *\n * @param   { number } num Number to convert. `0 < num <= 3999`.\n * @returns { string }\n */\nfunction numberToRoman (num) {\n  return [...(num) + '']\n    .map(n => +n)\n    .reverse()\n    .map((v, i) => ((v % 5 < 4)\n      ? (v < 5 ? '' : V[i]) + I[i].repeat(v % 5)\n      : I[i] + (v < 5 ? V[i] : I[i + 1])))\n    .reverse()\n    .join('');\n}\n\n/**\n * Helps to build text from words.\n */\nclass InlineTextBuilder {\n  /**\n   * Creates an instance of InlineTextBuilder.\n   *\n   * If `maxLineLength` is not provided then it is either `options.wordwrap` or unlimited.\n   *\n   * @param { Options } options           HtmlToText options.\n   * @param { number }  [ maxLineLength ] This builder will try to wrap text to fit this line length.\n   */\n  constructor (options, maxLineLength = undefined) {\n    /** @type { string[][] } */\n    this.lines = [];\n    /** @type { string[] }   */\n    this.nextLineWords = [];\n    this.maxLineLength = maxLineLength || options.wordwrap || Number.MAX_VALUE;\n    this.nextLineAvailableChars = this.maxLineLength;\n    this.wrapCharacters = get(options, ['longWordSplit', 'wrapCharacters']) || [];\n    this.forceWrapOnLimit = get(options, ['longWordSplit', 'forceWrapOnLimit']) || false;\n\n    this.stashedSpace = false;\n    this.wordBreakOpportunity = false;\n  }\n\n  /**\n   * Add a new word.\n   *\n   * @param { string } word A word to add.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  pushWord (word, noWrap = false) {\n    if (this.nextLineAvailableChars <= 0 && !noWrap) {\n      this.startNewLine();\n    }\n    const isLineStart = this.nextLineWords.length === 0;\n    const cost = word.length + (isLineStart ? 0 : 1);\n    if ((cost <= this.nextLineAvailableChars) || noWrap) { // Fits into available budget\n\n      this.nextLineWords.push(word);\n      this.nextLineAvailableChars -= cost;\n\n    } else { // Does not fit - try to split the word\n\n      // The word is moved to a new line - prefer to wrap between words.\n      const [first, ...rest] = this.splitLongWord(word);\n      if (!isLineStart) { this.startNewLine(); }\n      this.nextLineWords.push(first);\n      this.nextLineAvailableChars -= first.length;\n      for (const part of rest) {\n        this.startNewLine();\n        this.nextLineWords.push(part);\n        this.nextLineAvailableChars -= part.length;\n      }\n\n    }\n  }\n\n  /**\n   * Pop a word from the currently built line.\n   * This doesn't affect completed lines.\n   *\n   * @returns { string }\n   */\n  popWord () {\n    const lastWord = this.nextLineWords.pop();\n    if (lastWord !== undefined) {\n      const isLineStart = this.nextLineWords.length === 0;\n      const cost = lastWord.length + (isLineStart ? 0 : 1);\n      this.nextLineAvailableChars += cost;\n    }\n    return lastWord;\n  }\n\n  /**\n   * Concat a word to the last word already in the builder.\n   * Adds a new word in case there are no words yet in the last line.\n   *\n   * @param { string } word A word to be concatenated.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  concatWord (word, noWrap = false) {\n    if (this.wordBreakOpportunity && word.length > this.nextLineAvailableChars) {\n      this.pushWord(word, noWrap);\n      this.wordBreakOpportunity = false;\n    } else {\n      const lastWord = this.popWord();\n      this.pushWord((lastWord) ? lastWord.concat(word) : word, noWrap);\n    }\n  }\n\n  /**\n   * Add current line (and more empty lines if provided argument > 1) to the list of complete lines and start a new one.\n   *\n   * @param { number } n Number of line breaks that will be added to the resulting string.\n   */\n  startNewLine (n = 1) {\n    this.lines.push(this.nextLineWords);\n    if (n > 1) {\n      this.lines.push(...Array.from({ length: n - 1 }, () => []));\n    }\n    this.nextLineWords = [];\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * No words in this builder.\n   *\n   * @returns { boolean }\n   */\n  isEmpty () {\n    return this.lines.length === 0\n        && this.nextLineWords.length === 0;\n  }\n\n  clear () {\n    this.lines.length = 0;\n    this.nextLineWords.length = 0;\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * Join all lines of words inside the InlineTextBuilder into a complete string.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return [...this.lines, this.nextLineWords]\n      .map(words => words.join(' '))\n      .join('\\n');\n  }\n\n  /**\n   * Split a long word up to fit within the word wrap limit.\n   * Use either a character to split looking back from the word wrap limit,\n   * or truncate to the word wrap limit.\n   *\n   * @param   { string }   word Input word.\n   * @returns { string[] }      Parts of the word.\n   */\n  splitLongWord (word) {\n    const parts = [];\n    let idx = 0;\n    while (word.length > this.maxLineLength) {\n\n      const firstLine = word.substring(0, this.maxLineLength);\n      const remainingChars = word.substring(this.maxLineLength);\n\n      const splitIndex = firstLine.lastIndexOf(this.wrapCharacters[idx]);\n\n      if (splitIndex > -1) { // Found a character to split on\n\n        word = firstLine.substring(splitIndex + 1) + remainingChars;\n        parts.push(firstLine.substring(0, splitIndex + 1));\n\n      } else { // Not found a character to split on\n\n        idx++;\n        if (idx < this.wrapCharacters.length) { // There is next character to try\n\n          word = firstLine + remainingChars;\n\n        } else { // No more characters to try\n\n          if (this.forceWrapOnLimit) {\n            parts.push(firstLine);\n            word = remainingChars;\n            if (word.length > this.maxLineLength) {\n              continue;\n            }\n          } else {\n            word = firstLine + remainingChars;\n          }\n          break;\n\n        }\n\n      }\n\n    }\n    parts.push(word); // Add remaining part to array\n    return parts;\n  }\n}\n\n/* eslint-disable max-classes-per-file */\n\n\nclass StackItem {\n  constructor (next = null) { this.next = next; }\n\n  getRoot () { return (this.next) ? this.next : this; }\n}\n\nclass BlockStackItem extends StackItem {\n  constructor (options, next = null, leadingLineBreaks = 1, maxLineLength = undefined) {\n    super(next);\n    this.leadingLineBreaks = leadingLineBreaks;\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxLineLength);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass ListStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      interRowLineBreaks = 1,\n      leadingLineBreaks = 2,\n      maxLineLength = undefined,\n      maxPrefixLength = 0,\n      prefixAlign = 'left',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.maxPrefixLength = maxPrefixLength;\n    this.prefixAlign = prefixAlign;\n    this.interRowLineBreaks = interRowLineBreaks;\n  }\n}\n\nclass ListItemStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      leadingLineBreaks = 1,\n      maxLineLength = undefined,\n      prefix = '',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.prefix = prefix;\n  }\n}\n\nclass TableStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.rows = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableRowStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.cells = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableCellStackItem extends StackItem {\n  constructor (options, next = null, maxColumnWidth = undefined) {\n    super(next);\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxColumnWidth);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TransformerStackItem extends StackItem {\n  constructor (next = null, transform) {\n    super(next);\n    this.transform = transform;\n  }\n}\n\nfunction charactersToCodes (str) {\n  return [...str]\n    .map(c => '\\\\u' + c.charCodeAt(0).toString(16).padStart(4, '0'))\n    .join('');\n}\n\n/**\n * Helps to handle HTML whitespaces.\n *\n * @class WhitespaceProcessor\n */\nclass WhitespaceProcessor {\n\n  /**\n   * Creates an instance of WhitespaceProcessor.\n   *\n   * @param { Options } options    HtmlToText options.\n   * @memberof WhitespaceProcessor\n   */\n  constructor (options) {\n    this.whitespaceChars = (options.preserveNewlines)\n      ? options.whitespaceCharacters.replace(/\\n/g, '')\n      : options.whitespaceCharacters;\n    const whitespaceCodes = charactersToCodes(this.whitespaceChars);\n    this.leadingWhitespaceRe = new RegExp(`^[${whitespaceCodes}]`);\n    this.trailingWhitespaceRe = new RegExp(`[${whitespaceCodes}]$`);\n    this.allWhitespaceOrEmptyRe = new RegExp(`^[${whitespaceCodes}]*$`);\n    this.newlineOrNonWhitespaceRe = new RegExp(`(\\\\n|[^\\\\n${whitespaceCodes}])`, 'g');\n    this.newlineOrNonNewlineStringRe = new RegExp(`(\\\\n|[^\\\\n]+)`, 'g');\n\n    if (options.preserveNewlines) {\n\n      const wordOrNewlineRe = new RegExp(`\\\\n|[^\\\\n${whitespaceCodes}]+`, 'gm');\n\n      /**\n       * Shrink whitespaces and wrap text, add to the builder.\n       *\n       * @param { string }                  text              Input text.\n       * @param { InlineTextBuilder }       inlineTextBuilder A builder to receive processed text.\n       * @param { (str: string) => string } [ transform ]     A transform to be applied to words.\n       * @param { boolean }                 [noWrap] Don't wrap text even if the line is too long.\n       */\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordOrNewlineRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (m[0] === '\\n') {\n            inlineTextBuilder.startNewLine();\n          } else if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordOrNewlineRe.exec(text)) !== null) {\n            if (m[0] === '\\n') {\n              inlineTextBuilder.startNewLine();\n            } else {\n              inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n            }\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || (this.testTrailingWhitespace(text));\n        // No need to stash a space in case last added item was a new line,\n        // but that won't affect anything later anyway.\n      };\n\n    } else {\n\n      const wordRe = new RegExp(`[^${whitespaceCodes}]+`, 'g');\n\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordRe.exec(text)) !== null) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || this.testTrailingWhitespace(text);\n      };\n\n    }\n  }\n\n  /**\n   * Add text with only minimal processing.\n   * Everything between newlines considered a single word.\n   * No whitespace is trimmed.\n   * Not affected by preserveNewlines option - `\\n` always starts a new line.\n   *\n   * `noWrap` argument is `true` by default - this won't start a new line\n   * even if there is not enough space left in the current line.\n   *\n   * @param { string }            text              Input text.\n   * @param { InlineTextBuilder } inlineTextBuilder A builder to receive processed text.\n   * @param { boolean }           [noWrap] Don't wrap text even if the line is too long.\n   */\n  addLiteral (text, inlineTextBuilder, noWrap = true) {\n    if (!text) { return; }\n    const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n    let anyMatch = false;\n    let m = this.newlineOrNonNewlineStringRe.exec(text);\n    if (m) {\n      anyMatch = true;\n      if (m[0] === '\\n') {\n        inlineTextBuilder.startNewLine();\n      } else if (previouslyStashedSpace) {\n        inlineTextBuilder.pushWord(m[0], noWrap);\n      } else {\n        inlineTextBuilder.concatWord(m[0], noWrap);\n      }\n      while ((m = this.newlineOrNonNewlineStringRe.exec(text)) !== null) {\n        if (m[0] === '\\n') {\n          inlineTextBuilder.startNewLine();\n        } else {\n          inlineTextBuilder.pushWord(m[0], noWrap);\n        }\n      }\n    }\n    inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch);\n  }\n\n  /**\n   * Test whether the given text starts with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testLeadingWhitespace (text) {\n    return this.leadingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text ends with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testTrailingWhitespace (text) {\n    return this.trailingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text contains any non-whitespace characters.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testContainsWords (text) {\n    return !this.allWhitespaceOrEmptyRe.test(text);\n  }\n\n  /**\n   * Return the number of newlines if there are no words.\n   *\n   * If any word is found then return zero regardless of the actual number of newlines.\n   *\n   * @param   { string }  text  Input string.\n   * @returns { number }\n   */\n  countNewlinesNoWords (text) {\n    this.newlineOrNonWhitespaceRe.lastIndex = 0;\n    let counter = 0;\n    let match;\n    while ((match = this.newlineOrNonWhitespaceRe.exec(text)) !== null) {\n      if (match[0] === '\\n') {\n        counter++;\n      } else {\n        return 0;\n      }\n    }\n    return counter;\n  }\n\n}\n\n/**\n * Helps to build text from inline and block elements.\n *\n * @class BlockTextBuilder\n */\nclass BlockTextBuilder {\n\n  /**\n   * Creates an instance of BlockTextBuilder.\n   *\n   * @param { Options } options HtmlToText options.\n   * @param { import('selderee').Picker<DomNode, TagDefinition> } picker Selectors decision tree picker.\n   * @param { any} [metadata] Optional metadata for HTML document, for use in formatters.\n   */\n  constructor (options, picker, metadata = undefined) {\n    this.options = options;\n    this.picker = picker;\n    this.metadata = metadata;\n    this.whitespaceProcessor = new WhitespaceProcessor(options);\n    /** @type { StackItem } */\n    this._stackItem = new BlockStackItem(options);\n    /** @type { TransformerStackItem } */\n    this._wordTransformer = undefined;\n  }\n\n  /**\n   * Put a word-by-word transform function onto the transformations stack.\n   *\n   * Mainly used for uppercasing. Can be bypassed to add unformatted text such as URLs.\n   *\n   * Word transformations applied before wrapping.\n   *\n   * @param { (str: string) => string } wordTransform Word transformation function.\n   */\n  pushWordTransform (wordTransform) {\n    this._wordTransformer = new TransformerStackItem(this._wordTransformer, wordTransform);\n  }\n\n  /**\n   * Remove a function from the word transformations stack.\n   *\n   * @returns { (str: string) => string } A function that was removed.\n   */\n  popWordTransform () {\n    if (!this._wordTransformer) { return undefined; }\n    const transform = this._wordTransformer.transform;\n    this._wordTransformer = this._wordTransformer.next;\n    return transform;\n  }\n\n  /**\n   * Ignore wordwrap option in followup inline additions and disable automatic wrapping.\n   */\n  startNoWrap () {\n    this._stackItem.isNoWrap = true;\n  }\n\n  /**\n   * Return automatic wrapping to behavior defined by options.\n   */\n  stopNoWrap () {\n    this._stackItem.isNoWrap = false;\n  }\n\n  /** @returns { (str: string) => string } */\n  _getCombinedWordTransformer () {\n    const wt = (this._wordTransformer)\n      ? ((str) => applyTransformer(str, this._wordTransformer))\n      : undefined;\n    const ce = this.options.encodeCharacters;\n    return (wt)\n      ? ((ce) ? (str) => ce(wt(str)) : wt)\n      : ce;\n  }\n\n  _popStackItem () {\n    const item = this._stackItem;\n    this._stackItem = item.next;\n    return item;\n  }\n\n  /**\n   * Add a line break into currently built block.\n   */\n  addLineBreak () {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += '\\n';\n    } else {\n      this._stackItem.inlineTextBuilder.startNewLine();\n    }\n  }\n\n  /**\n   * Allow to break line in case directly following text will not fit.\n   */\n  addWordBreakOpportunity () {\n    if (\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    ) {\n      this._stackItem.inlineTextBuilder.wordBreakOpportunity = true;\n    }\n  }\n\n  /**\n   * Add a node inline into the currently built block.\n   *\n   * @param { string } str\n   * Text content of a node to add.\n   *\n   * @param { object } [param1]\n   * Object holding the parameters of the operation.\n   *\n   * @param { boolean } [param1.noWordTransform]\n   * Ignore word transformers if there are any.\n   * Don't encode characters as well.\n   * (Use this for things like URL addresses).\n   */\n  addInline (str, { noWordTransform = false } = {}) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (\n      str.length === 0 || // empty string\n      (\n        this._stackItem.stashedLineBreaks && // stashed linebreaks make whitespace irrelevant\n        !this.whitespaceProcessor.testContainsWords(str) // no words to add\n      )\n    ) { return; }\n\n    if (this.options.preserveNewlines) {\n      const newlinesNumber = this.whitespaceProcessor.countNewlinesNoWords(str);\n      if (newlinesNumber > 0) {\n        this._stackItem.inlineTextBuilder.startNewLine(newlinesNumber);\n        // keep stashedLineBreaks unchanged\n        return;\n      }\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.shrinkWrapAdd(\n      str,\n      this._stackItem.inlineTextBuilder,\n      (noWordTransform) ? undefined : this._getCombinedWordTransformer(),\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0; // inline text doesn't introduce line breaks\n  }\n\n  /**\n   * Add a string inline into the currently built block.\n   *\n   * Use this for markup elements that don't have to adhere\n   * to text layout rules.\n   *\n   * @param { string } str Text to add.\n   */\n  addLiteral (str) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (str.length === 0) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.addLiteral(\n      str,\n      this._stackItem.inlineTextBuilder,\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0;\n  }\n\n  /**\n   * Start building a new block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any preceding block.\n   *\n   * @param { number }  [param0.reservedLineLength]\n   * Reserve this number of characters on each line for block markup.\n   *\n   * @param { boolean } [param0.isPre]\n   * Should HTML whitespace be preserved inside this block.\n   */\n  openBlock ({ leadingLineBreaks = 1, reservedLineLength = 0, isPre = false } = {}) {\n    const maxLineLength = Math.max(20, this._stackItem.inlineTextBuilder.maxLineLength - reservedLineLength);\n    this._stackItem = new BlockStackItem(\n      this.options,\n      this._stackItem,\n      leadingLineBreaks,\n      maxLineLength\n    );\n    if (isPre) { this._stackItem.isPre = true; }\n  }\n\n  /**\n   * Finalize currently built block, add it's content to the parent block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any following block.\n   *\n   * @param { (str: string) => string } [param0.blockTransform]\n   * A function to transform the block text before adding to the parent block.\n   * This happens after word wrap and should be used in combination with reserved line length\n   * in order to keep line lengths correct.\n   * Used for whole block markup.\n   */\n  closeBlock ({ trailingLineBreaks = 1, blockTransform = undefined } = {}) {\n    const block = this._popStackItem();\n    const blockText = (blockTransform) ? blockTransform(getText(block)) : getText(block);\n    addText(this._stackItem, blockText, block.leadingLineBreaks, Math.max(block.stashedLineBreaks, trailingLineBreaks));\n  }\n\n  /**\n   * Start building a new list.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.maxPrefixLength]\n   * Length of the longest list item prefix.\n   * If not supplied or too small then list items won't be aligned properly.\n   *\n   * @param { 'left' | 'right' } [param0.prefixAlign]\n   * Specify how prefixes of different lengths have to be aligned\n   * within a column.\n   *\n   * @param { number } [param0.interRowLineBreaks]\n   * Minimum number of line breaks between list items.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any preceding block.\n   */\n  openList ({ maxPrefixLength = 0, prefixAlign = 'left', interRowLineBreaks = 1, leadingLineBreaks = 2 } = {}) {\n    this._stackItem = new ListStackItem(this.options, this._stackItem, {\n      interRowLineBreaks: interRowLineBreaks,\n      leadingLineBreaks: leadingLineBreaks,\n      maxLineLength: this._stackItem.inlineTextBuilder.maxLineLength,\n      maxPrefixLength: maxPrefixLength,\n      prefixAlign: prefixAlign\n    });\n  }\n\n  /**\n   * Start building a new list item.\n   *\n   * @param {object} param0\n   * Object holding the parameters of the list item.\n   *\n   * @param { string } [param0.prefix]\n   * Prefix for this list item (item number, bullet point, etc).\n   */\n  openListItem ({ prefix = '' } = {}) {\n    if (!(this._stackItem instanceof ListStackItem)) {\n      throw new Error('Can\\'t add a list item to something that is not a list! Check the formatter.');\n    }\n    const list = this._stackItem;\n    const prefixLength = Math.max(prefix.length, list.maxPrefixLength);\n    const maxLineLength = Math.max(20, list.inlineTextBuilder.maxLineLength - prefixLength);\n    this._stackItem = new ListItemStackItem(this.options, list, {\n      prefix: prefix,\n      maxLineLength: maxLineLength,\n      leadingLineBreaks: list.interRowLineBreaks\n    });\n  }\n\n  /**\n   * Finalize currently built list item, add it's content to the parent list.\n   */\n  closeListItem () {\n    const listItem = this._popStackItem();\n    const list = listItem.next;\n\n    const prefixLength = Math.max(listItem.prefix.length, list.maxPrefixLength);\n    const spacing = '\\n' + ' '.repeat(prefixLength);\n    const prefix = (list.prefixAlign === 'right')\n      ? listItem.prefix.padStart(prefixLength)\n      : listItem.prefix.padEnd(prefixLength);\n    const text = prefix + getText(listItem).replace(/\\n/g, spacing);\n\n    addText(\n      list,\n      text,\n      listItem.leadingLineBreaks,\n      Math.max(listItem.stashedLineBreaks, list.interRowLineBreaks)\n    );\n  }\n\n  /**\n   * Finalize currently built list, add it's content to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any following block.\n   */\n  closeList ({ trailingLineBreaks = 2 } = {}) {\n    const list = this._popStackItem();\n    const text = getText(list);\n    if (text) {\n      addText(this._stackItem, text, list.leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Start building a table.\n   */\n  openTable () {\n    this._stackItem = new TableStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table row.\n   */\n  openTableRow () {\n    if (!(this._stackItem instanceof TableStackItem)) {\n      throw new Error('Can\\'t add a table row to something that is not a table! Check the formatter.');\n    }\n    this._stackItem = new TableRowStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table cell.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.maxColumnWidth]\n   * Wrap cell content to this width. Fall back to global wordwrap value if undefined.\n   */\n  openTableCell ({ maxColumnWidth = undefined } = {}) {\n    if (!(this._stackItem instanceof TableRowStackItem)) {\n      throw new Error('Can\\'t add a table cell to something that is not a table row! Check the formatter.');\n    }\n    this._stackItem = new TableCellStackItem(this.options, this._stackItem, maxColumnWidth);\n  }\n\n  /**\n   * Finalize currently built table cell and add it to parent table row's cells.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.colspan] How many columns this cell should occupy.\n   * @param { number } [param0.rowspan] How many rows this cell should occupy.\n   */\n  closeTableCell ({ colspan = 1, rowspan = 1 } = {}) {\n    const cell = this._popStackItem();\n    const text = trimCharacter(getText(cell), '\\n');\n    cell.next.cells.push({ colspan: colspan, rowspan: rowspan, text: text });\n  }\n\n  /**\n   * Finalize currently built table row and add it to parent table's rows.\n   */\n  closeTableRow () {\n    const row = this._popStackItem();\n    row.next.rows.push(row.cells);\n  }\n\n  /**\n   * Finalize currently built table and add the rendered text to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the table.\n   *\n   * @param { TablePrinter } param0.tableToString\n   * A function to convert a table of stringified cells into a complete table.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This table should have at least this number of line breaks to separate if from any preceding block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This table should have at least this number of line breaks to separate it from any following block.\n   */\n  closeTable ({ tableToString, leadingLineBreaks = 2, trailingLineBreaks = 2 }) {\n    const table = this._popStackItem();\n    const output = tableToString(table.rows);\n    if (output) {\n      addText(this._stackItem, output, leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Return the rendered text content of this builder.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return getText(this._stackItem.getRoot());\n    // There should only be the root item if everything is closed properly.\n  }\n\n}\n\nfunction getText (stackItem) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can be requested for text contents.');\n  }\n  return (stackItem.inlineTextBuilder.isEmpty())\n    ? stackItem.rawText\n    : stackItem.rawText + stackItem.inlineTextBuilder.toString();\n}\n\nfunction addText (stackItem, text, leadingLineBreaks, trailingLineBreaks) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can contain text.');\n  }\n  const parentText = getText(stackItem);\n  const lineBreaks = Math.max(stackItem.stashedLineBreaks, leadingLineBreaks);\n  stackItem.inlineTextBuilder.clear();\n  if (parentText) {\n    stackItem.rawText = parentText + '\\n'.repeat(lineBreaks) + text;\n  } else {\n    stackItem.rawText = text;\n    stackItem.leadingLineBreaks = lineBreaks;\n  }\n  stackItem.stashedLineBreaks = trailingLineBreaks;\n}\n\n/**\n * @param { string } str A string to transform.\n * @param { TransformerStackItem } transformer A transformer item (with possible continuation).\n * @returns { string }\n */\nfunction applyTransformer (str, transformer) {\n  return ((transformer) ? applyTransformer(transformer.transform(str), transformer.next) : str);\n}\n\n/**\n * Compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options (defaults, formatters, user options merged, deduplicated).\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile$1 (options = {}) {\n  const selectorsWithoutFormat = options.selectors.filter(s => !s.format);\n  if (selectorsWithoutFormat.length) {\n    throw new Error(\n      'Following selectors have no specified format: ' +\n      selectorsWithoutFormat.map(s => `\\`${s.selector}\\``).join(', ')\n    );\n  }\n  const picker = new selderee__WEBPACK_IMPORTED_MODULE_4__.DecisionTree(\n    options.selectors.map(s => [s.selector, s])\n  ).build(_selderee_plugin_htmlparser2__WEBPACK_IMPORTED_MODULE_0__.hp2Builder);\n\n  if (typeof options.encodeCharacters !== 'function') {\n    options.encodeCharacters = makeReplacerFromDict(options.encodeCharacters);\n  }\n\n  const baseSelectorsPicker = new selderee__WEBPACK_IMPORTED_MODULE_4__.DecisionTree(\n    options.baseElements.selectors.map((s, i) => [s, i + 1])\n  ).build(_selderee_plugin_htmlparser2__WEBPACK_IMPORTED_MODULE_0__.hp2Builder);\n  function findBaseElements (dom) {\n    return findBases(dom, options, baseSelectorsPicker);\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk,\n    function (dom, builder) {\n      builder.addInline(options.limits.ellipsis || '');\n    }\n  );\n\n  return function (html, metadata = undefined) {\n    return process(html, metadata, options, picker, findBaseElements, limitedWalk);\n  };\n}\n\n\n/**\n * Convert given HTML according to preprocessed options.\n *\n * @param { string } html HTML content to convert.\n * @param { any } metadata Optional metadata for HTML document, for use in formatters.\n * @param { Options } options HtmlToText options (preprocessed).\n * @param { import('selderee').Picker<DomNode, TagDefinition> } picker\n * Tag definition picker for DOM nodes processing.\n * @param { (dom: DomNode[]) => DomNode[] } findBaseElements\n * Function to extract elements from HTML DOM\n * that will only be present in the output text.\n * @param { RecursiveCallback } walk Recursive callback.\n * @returns { string }\n */\nfunction process (html, metadata, options, picker, findBaseElements, walk) {\n  const maxInputLength = options.limits.maxInputLength;\n  if (maxInputLength && html && html.length > maxInputLength) {\n    console.warn(\n      `Input length ${html.length} is above allowed limit of ${maxInputLength}. Truncating without ellipsis.`\n    );\n    html = html.substring(0, maxInputLength);\n  }\n\n  const document = (0,htmlparser2__WEBPACK_IMPORTED_MODULE_1__.parseDocument)(html, { decodeEntities: options.decodeEntities });\n  const bases = findBaseElements(document.children);\n  const builder = new BlockTextBuilder(options, picker, metadata);\n  walk(bases, builder);\n  return builder.toString();\n}\n\n\nfunction findBases (dom, options, baseSelectorsPicker) {\n  const results = [];\n\n  function recursiveWalk (walk, /** @type { DomNode[] } */ dom) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    for (const elem of dom) {\n      if (elem.type !== 'tag') {\n        continue;\n      }\n      const pickedSelectorIndex = baseSelectorsPicker.pick1(elem);\n      if (pickedSelectorIndex > 0) {\n        results.push({ selectorIndex: pickedSelectorIndex, element: elem });\n      } else if (elem.children) {\n        walk(elem.children);\n      }\n      if (results.length >= options.limits.maxBaseElements) {\n        return;\n      }\n    }\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk\n  );\n  limitedWalk(dom);\n\n  if (options.baseElements.orderBy !== 'occurrence') { // 'selectors'\n    results.sort((a, b) => a.selectorIndex - b.selectorIndex);\n  }\n  return (options.baseElements.returnDomByDefault && results.length === 0)\n    ? dom\n    : results.map(x => x.element);\n}\n\n/**\n * Function to walk through DOM nodes and accumulate their string representations.\n *\n * @param   { RecursiveCallback } walk    Recursive callback.\n * @param   { DomNode[] }         [dom]   Nodes array to process.\n * @param   { BlockTextBuilder }  builder Passed around to accumulate output text.\n * @private\n */\nfunction recursiveWalk (walk, dom, builder) {\n  if (!dom) { return; }\n\n  const options = builder.options;\n\n  const tooManyChildNodes = dom.length > options.limits.maxChildNodes;\n  if (tooManyChildNodes) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    dom.push({\n      data: options.limits.ellipsis,\n      type: 'text'\n    });\n  }\n\n  for (const elem of dom) {\n    switch (elem.type) {\n      case 'text': {\n        builder.addInline(elem.data);\n        break;\n      }\n      case 'tag': {\n        const tagDefinition = builder.picker.pick1(elem);\n        const format = options.formatters[tagDefinition.format];\n        format(elem, walk, builder, tagDefinition.options || {});\n        break;\n      }\n    }\n  }\n\n  return;\n}\n\n/**\n * @param { Object<string,string | false> } dict\n * A dictionary where keys are characters to replace\n * and values are replacement strings.\n *\n * First code point from dict keys is used.\n * Compound emojis with ZWJ are not supported (not until Node 16).\n *\n * @returns { ((str: string) => string) | undefined }\n */\nfunction makeReplacerFromDict (dict) {\n  if (!dict || Object.keys(dict).length === 0) {\n    return undefined;\n  }\n  /** @type { [string, string][] } */\n  const entries = Object.entries(dict).filter(([, v]) => v !== false);\n  const regex = new RegExp(\n    entries\n      .map(([c]) => `(${unicodeEscape([...c][0])})`)\n      .join('|'),\n    'g'\n  );\n  const values = entries.map(([, v]) => v);\n  const replacer = (m, ...cgs) => values[cgs.findIndex(cg => cg)];\n  return (str) => str.replace(regex, replacer);\n}\n\n/**\n * Dummy formatter that discards the input and does nothing.\n *\n * @type { FormatCallback }\n */\nfunction formatSkip (elem, walk, builder, formatOptions) {\n  /* do nothing */\n}\n\n/**\n * Insert the given string literal inline instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineString (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.string || '');\n}\n\n/**\n * Insert a block with the given string literal instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockString (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addLiteral(formatOptions.string || '');\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process an inline-level element.\n *\n * @type { FormatCallback }\n */\nfunction formatInline (elem, walk, builder, formatOptions) {\n  walk(elem.children, builder);\n}\n\n/**\n * Process a block-level container.\n *\n * @type { FormatCallback }\n */\nfunction formatBlock$1 (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\nfunction renderOpenTag (elem) {\n  const attrs = (elem.attribs && elem.attribs.length)\n    ? ' ' + Object.entries(elem.attribs)\n      .map(([k, v]) => ((v === '') ? k : `${k}=${v.replace(/\"/g, '&quot;')}`))\n      .join(' ')\n    : '';\n  return `<${elem.name}${attrs}>`;\n}\n\nfunction renderCloseTag (elem) {\n  return `</${elem.name}>`;\n}\n\n/**\n * Render an element as inline HTML tag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineTag (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element as HTML block bag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockTag (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render an element with all it's children as inline HTML.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineHtml (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(\n    (0,dom_serializer__WEBPACK_IMPORTED_MODULE_3__.render)(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element with all it's children as HTML block.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockHtml (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(\n    (0,dom_serializer__WEBPACK_IMPORTED_MODULE_3__.render)(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render inline element wrapped with given strings.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineSurround (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.prefix || '');\n  walk(elem.children, builder);\n  builder.addLiteral(formatOptions.suffix || '');\n}\n\nvar genericFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  block: formatBlock$1,\n  blockHtml: formatBlockHtml,\n  blockString: formatBlockString,\n  blockTag: formatBlockTag,\n  inline: formatInline,\n  inlineHtml: formatInlineHtml,\n  inlineString: formatInlineString,\n  inlineSurround: formatInlineSurround,\n  inlineTag: formatInlineTag,\n  skip: formatSkip\n});\n\nfunction getRow (matrix, j) {\n  if (!matrix[j]) { matrix[j] = []; }\n  return matrix[j];\n}\n\nfunction findFirstVacantIndex (row, x = 0) {\n  while (row[x]) { x++; }\n  return x;\n}\n\nfunction transposeInPlace (matrix, maxSize) {\n  for (let i = 0; i < maxSize; i++) {\n    const rowI = getRow(matrix, i);\n    for (let j = 0; j < i; j++) {\n      const rowJ = getRow(matrix, j);\n      if (rowI[j] || rowJ[i]) {\n        const temp = rowI[j];\n        rowI[j] = rowJ[i];\n        rowJ[i] = temp;\n      }\n    }\n  }\n}\n\nfunction putCellIntoLayout (cell, layout, baseRow, baseCol) {\n  for (let r = 0; r < cell.rowspan; r++) {\n    const layoutRow = getRow(layout, baseRow + r);\n    for (let c = 0; c < cell.colspan; c++) {\n      layoutRow[baseCol + c] = cell;\n    }\n  }\n}\n\nfunction getOrInitOffset (offsets, index) {\n  if (offsets[index] === undefined) {\n    offsets[index] = (index === 0) ? 0 : 1 + getOrInitOffset(offsets, index - 1);\n  }\n  return offsets[index];\n}\n\nfunction updateOffset (offsets, base, span, value) {\n  offsets[base + span] = Math.max(\n    getOrInitOffset(offsets, base + span),\n    getOrInitOffset(offsets, base) + value\n  );\n}\n\n/**\n * Render a table into a string.\n * Cells can contain multiline text and span across multiple rows and columns.\n *\n * Modifies cells to add lines array.\n *\n * @param { TablePrinterCell[][] } tableRows Table to render.\n * @param { number } rowSpacing Number of spaces between columns.\n * @param { number } colSpacing Number of empty lines between rows.\n * @returns { string }\n */\nfunction tableToString (tableRows, rowSpacing, colSpacing) {\n  const layout = [];\n  let colNumber = 0;\n  const rowNumber = tableRows.length;\n  const rowOffsets = [0];\n  // Fill the layout table and row offsets row-by-row.\n  for (let j = 0; j < rowNumber; j++) {\n    const layoutRow = getRow(layout, j);\n    const cells = tableRows[j];\n    let x = 0;\n    for (let i = 0; i < cells.length; i++) {\n      const cell = cells[i];\n      x = findFirstVacantIndex(layoutRow, x);\n      putCellIntoLayout(cell, layout, j, x);\n      x += cell.colspan;\n      cell.lines = cell.text.split('\\n');\n      const cellHeight = cell.lines.length;\n      updateOffset(rowOffsets, j, cell.rowspan, cellHeight + rowSpacing);\n    }\n    colNumber = (layoutRow.length > colNumber) ? layoutRow.length : colNumber;\n  }\n\n  transposeInPlace(layout, (rowNumber > colNumber) ? rowNumber : colNumber);\n\n  const outputLines = [];\n  const colOffsets = [0];\n  // Fill column offsets and output lines column-by-column.\n  for (let x = 0; x < colNumber; x++) {\n    let y = 0;\n    let cell;\n    const rowsInThisColumn = Math.min(rowNumber, layout[x].length);\n    while (y < rowsInThisColumn) {\n      cell = layout[x][y];\n      if (cell) {\n        if (!cell.rendered) {\n          let cellWidth = 0;\n          for (let j = 0; j < cell.lines.length; j++) {\n            const line = cell.lines[j];\n            const lineOffset = rowOffsets[y] + j;\n            outputLines[lineOffset] = (outputLines[lineOffset] || '').padEnd(colOffsets[x]) + line;\n            cellWidth = (line.length > cellWidth) ? line.length : cellWidth;\n          }\n          updateOffset(colOffsets, x, cell.colspan, cellWidth + colSpacing);\n          cell.rendered = true;\n        }\n        y += cell.rowspan;\n      } else {\n        const lineOffset = rowOffsets[y];\n        outputLines[lineOffset] = (outputLines[lineOffset] || '');\n        y++;\n      }\n    }\n  }\n\n  return outputLines.join('\\n');\n}\n\n/**\n * Process a line-break.\n *\n * @type { FormatCallback }\n */\nfunction formatLineBreak (elem, walk, builder, formatOptions) {\n  builder.addLineBreak();\n}\n\n/**\n * Process a `wbr` tag (word break opportunity).\n *\n * @type { FormatCallback }\n */\nfunction formatWbr (elem, walk, builder, formatOptions) {\n  builder.addWordBreakOpportunity();\n}\n\n/**\n * Process a horizontal line.\n *\n * @type { FormatCallback }\n */\nfunction formatHorizontalLine (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addInline('-'.repeat(formatOptions.length || builder.options.wordwrap || 40));\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a paragraph.\n *\n * @type { FormatCallback }\n */\nfunction formatParagraph (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a preformatted content.\n *\n * @type { FormatCallback }\n */\nfunction formatPre (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    isPre: true,\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a heading.\n *\n * @type { FormatCallback }\n */\nfunction formatHeading (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  if (formatOptions.uppercase !== false) {\n    builder.pushWordTransform(str => str.toUpperCase());\n    walk(elem.children, builder);\n    builder.popWordTransform();\n  } else {\n    walk(elem.children, builder);\n  }\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a blockquote.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockquote (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2,\n    reservedLineLength: 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({\n    trailingLineBreaks: formatOptions.trailingLineBreaks || 2,\n    blockTransform: str => ((formatOptions.trimEmptyLines !== false) ? trimCharacter(str, '\\n') : str)\n      .split('\\n')\n      .map(line => '> ' + line)\n      .join('\\n')\n  });\n}\n\nfunction withBrackets (str, brackets) {\n  if (!brackets) { return str; }\n\n  const lbr = (typeof brackets[0] === 'string')\n    ? brackets[0]\n    : '[';\n  const rbr = (typeof brackets[1] === 'string')\n    ? brackets[1]\n    : ']';\n  return lbr + str + rbr;\n}\n\nfunction pathRewrite (path, rewriter, baseUrl, metadata, elem) {\n  const modifiedPath = (typeof rewriter === 'function')\n    ? rewriter(path, metadata, elem)\n    : path;\n  return (modifiedPath[0] === '/' && baseUrl)\n    ? trimCharacterEnd(baseUrl, '/') + modifiedPath\n    : modifiedPath;\n}\n\n/**\n * Process an image.\n *\n * @type { FormatCallback }\n */\nfunction formatImage (elem, walk, builder, formatOptions) {\n  const attribs = elem.attribs || {};\n  const alt = (attribs.alt)\n    ? attribs.alt\n    : '';\n  const src = (!attribs.src)\n    ? ''\n    : pathRewrite(attribs.src, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n  const text = (!src)\n    ? alt\n    : (!alt)\n      ? withBrackets(src, formatOptions.linkBrackets)\n      : alt + ' ' + withBrackets(src, formatOptions.linkBrackets);\n\n  builder.addInline(text, { noWordTransform: true });\n}\n\n// a img baseUrl\n// a img pathRewrite\n// a img linkBrackets\n\n// a     ignoreHref: false\n//            ignoreText ?\n// a     noAnchorUrl: true\n//            can be replaced with selector\n// a     hideLinkHrefIfSameAsText: false\n//            how to compare, what to show (text, href, normalized) ?\n// a     mailto protocol removed without options\n\n// a     protocols: mailto, tel, ...\n//            can be matched with selector?\n\n// anchors, protocols - only if no pathRewrite fn is provided\n\n// normalize-url ?\n\n// a\n// a[href^=\"#\"] - format:skip by default\n// a[href^=\"mailto:\"] - ?\n\n/**\n * Process an anchor.\n *\n * @type { FormatCallback }\n */\nfunction formatAnchor (elem, walk, builder, formatOptions) {\n  function getHref () {\n    if (formatOptions.ignoreHref) { return ''; }\n    if (!elem.attribs || !elem.attribs.href) { return ''; }\n    let href = elem.attribs.href.replace(/^mailto:/, '');\n    if (formatOptions.noAnchorUrl && href[0] === '#') { return ''; }\n    href = pathRewrite(href, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n    return href;\n  }\n  const href = getHref();\n  if (!href) {\n    walk(elem.children, builder);\n  } else {\n    let text = '';\n    builder.pushWordTransform(\n      str => {\n        if (str) { text += str; }\n        return str;\n      }\n    );\n    walk(elem.children, builder);\n    builder.popWordTransform();\n\n    const hideSameLink = formatOptions.hideLinkHrefIfSameAsText && href === text;\n    if (!hideSameLink) {\n      builder.addInline(\n        (!text)\n          ? href\n          : ' ' + withBrackets(href, formatOptions.linkBrackets),\n        { noWordTransform: true }\n      );\n    }\n  }\n}\n\n/**\n * @param { DomNode }           elem               List items with their prefixes.\n * @param { RecursiveCallback } walk               Recursive callback to process child nodes.\n * @param { BlockTextBuilder }  builder            Passed around to accumulate output text.\n * @param { FormatOptions }     formatOptions      Options specific to a formatter.\n * @param { () => string }      nextPrefixCallback Function that returns increasing index each time it is called.\n */\nfunction formatList (elem, walk, builder, formatOptions, nextPrefixCallback) {\n  const isNestedList = get(elem, ['parent', 'name']) === 'li';\n\n  // With Roman numbers, index length is not as straightforward as with Arabic numbers or letters,\n  // so the dumb length comparison is the most robust way to get the correct value.\n  let maxPrefixLength = 0;\n  const listItems = (elem.children || [])\n    // it might be more accurate to check only for html spaces here, but no significant benefit\n    .filter(child => child.type !== 'text' || !/^\\s*$/.test(child.data))\n    .map(function (child) {\n      if (child.name !== 'li') {\n        return { node: child, prefix: '' };\n      }\n      const prefix = (isNestedList)\n        ? nextPrefixCallback().trimStart()\n        : nextPrefixCallback();\n      if (prefix.length > maxPrefixLength) { maxPrefixLength = prefix.length; }\n      return { node: child, prefix: prefix };\n    });\n  if (!listItems.length) { return; }\n\n  builder.openList({\n    interRowLineBreaks: 1,\n    leadingLineBreaks: isNestedList ? 1 : (formatOptions.leadingLineBreaks || 2),\n    maxPrefixLength: maxPrefixLength,\n    prefixAlign: 'left'\n  });\n\n  for (const { node, prefix } of listItems) {\n    builder.openListItem({ prefix: prefix });\n    walk([node], builder);\n    builder.closeListItem();\n  }\n\n  builder.closeList({ trailingLineBreaks: isNestedList ? 1 : (formatOptions.trailingLineBreaks || 2) });\n}\n\n/**\n * Process an unordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatUnorderedList (elem, walk, builder, formatOptions) {\n  const prefix = formatOptions.itemPrefix || ' * ';\n  return formatList(elem, walk, builder, formatOptions, () => prefix);\n}\n\n/**\n * Process an ordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatOrderedList (elem, walk, builder, formatOptions) {\n  let nextIndex = Number(elem.attribs.start || '1');\n  const indexFunction = getOrderedListIndexFunction(elem.attribs.type);\n  const nextPrefixCallback = () => ' ' + indexFunction(nextIndex++) + '. ';\n  return formatList(elem, walk, builder, formatOptions, nextPrefixCallback);\n}\n\n/**\n * Return a function that can be used to generate index markers of a specified format.\n *\n * @param   { string } [olType='1'] Marker type.\n * @returns { (i: number) => string }\n */\nfunction getOrderedListIndexFunction (olType = '1') {\n  switch (olType) {\n    case 'a': return (i) => numberToLetterSequence(i, 'a');\n    case 'A': return (i) => numberToLetterSequence(i, 'A');\n    case 'i': return (i) => numberToRoman(i).toLowerCase();\n    case 'I': return (i) => numberToRoman(i);\n    case '1':\n    default: return (i) => (i).toString();\n  }\n}\n\n/**\n * Given a list of class and ID selectors (prefixed with '.' and '#'),\n * return them as separate lists of names without prefixes.\n *\n * @param { string[] } selectors Class and ID selectors (`[\".class\", \"#id\"]` etc).\n * @returns { { classes: string[], ids: string[] } }\n */\nfunction splitClassesAndIds (selectors) {\n  const classes = [];\n  const ids = [];\n  for (const selector of selectors) {\n    if (selector.startsWith('.')) {\n      classes.push(selector.substring(1));\n    } else if (selector.startsWith('#')) {\n      ids.push(selector.substring(1));\n    }\n  }\n  return { classes: classes, ids: ids };\n}\n\nfunction isDataTable (attr, tables) {\n  if (tables === true) { return true; }\n  if (!attr) { return false; }\n\n  const { classes, ids } = splitClassesAndIds(tables);\n  const attrClasses = (attr['class'] || '').split(' ');\n  const attrIds = (attr['id'] || '').split(' ');\n\n  return attrClasses.some(x => classes.includes(x)) || attrIds.some(x => ids.includes(x));\n}\n\n/**\n * Process a table (either as a container or as a data table, depending on options).\n *\n * @type { FormatCallback }\n */\nfunction formatTable (elem, walk, builder, formatOptions) {\n  return isDataTable(elem.attribs, builder.options.tables)\n    ? formatDataTable(elem, walk, builder, formatOptions)\n    : formatBlock(elem, walk, builder, formatOptions);\n}\n\nfunction formatBlock (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks });\n}\n\n/**\n * Process a data table.\n *\n * @type { FormatCallback }\n */\nfunction formatDataTable (elem, walk, builder, formatOptions) {\n  builder.openTable();\n  elem.children.forEach(walkTable);\n  builder.closeTable({\n    tableToString: (rows) => tableToString(rows, formatOptions.rowSpacing ?? 0, formatOptions.colSpacing ?? 3),\n    leadingLineBreaks: formatOptions.leadingLineBreaks,\n    trailingLineBreaks: formatOptions.trailingLineBreaks\n  });\n\n  function formatCell (cellNode) {\n    const colspan = +get(cellNode, ['attribs', 'colspan']) || 1;\n    const rowspan = +get(cellNode, ['attribs', 'rowspan']) || 1;\n    builder.openTableCell({ maxColumnWidth: formatOptions.maxColumnWidth });\n    walk(cellNode.children, builder);\n    builder.closeTableCell({ colspan: colspan, rowspan: rowspan });\n  }\n\n  function walkTable (elem) {\n    if (elem.type !== 'tag') { return; }\n\n    const formatHeaderCell = (formatOptions.uppercaseHeaderCells !== false)\n      ? (cellNode) => {\n        builder.pushWordTransform(str => str.toUpperCase());\n        formatCell(cellNode);\n        builder.popWordTransform();\n      }\n      : formatCell;\n\n    switch (elem.name) {\n      case 'thead':\n      case 'tbody':\n      case 'tfoot':\n      case 'center':\n        elem.children.forEach(walkTable);\n        return;\n\n      case 'tr': {\n        builder.openTableRow();\n        for (const childOfTr of elem.children) {\n          if (childOfTr.type !== 'tag') { continue; }\n          switch (childOfTr.name) {\n            case 'th': {\n              formatHeaderCell(childOfTr);\n              break;\n            }\n            case 'td': {\n              formatCell(childOfTr);\n              break;\n            }\n              // do nothing\n          }\n        }\n        builder.closeTableRow();\n        break;\n      }\n        // do nothing\n    }\n  }\n}\n\nvar textFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  anchor: formatAnchor,\n  blockquote: formatBlockquote,\n  dataTable: formatDataTable,\n  heading: formatHeading,\n  horizontalLine: formatHorizontalLine,\n  image: formatImage,\n  lineBreak: formatLineBreak,\n  orderedList: formatOrderedList,\n  paragraph: formatParagraph,\n  pre: formatPre,\n  table: formatTable,\n  unorderedList: formatUnorderedList,\n  wbr: formatWbr\n});\n\n/**\n * Default options.\n *\n * @constant\n * @type { Options }\n * @default\n * @private\n */\nconst DEFAULT_OPTIONS = {\n  baseElements: {\n    selectors: [ 'body' ],\n    orderBy: 'selectors', // 'selectors' | 'occurrence'\n    returnDomByDefault: true\n  },\n  decodeEntities: true,\n  encodeCharacters: {},\n  formatters: {},\n  limits: {\n    ellipsis: '...',\n    maxBaseElements: undefined,\n    maxChildNodes: undefined,\n    maxDepth: undefined,\n    maxInputLength: (1 << 24) // 16_777_216\n  },\n  longWordSplit: {\n    forceWrapOnLimit: false,\n    wrapCharacters: []\n  },\n  preserveNewlines: false,\n  selectors: [\n    { selector: '*', format: 'inline' },\n    {\n      selector: 'a',\n      format: 'anchor',\n      options: {\n        baseUrl: null,\n        hideLinkHrefIfSameAsText: false,\n        ignoreHref: false,\n        linkBrackets: ['[', ']'],\n        noAnchorUrl: true\n      }\n    },\n    { selector: 'article', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'aside', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'blockquote',\n      format: 'blockquote',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2, trimEmptyLines: true }\n    },\n    { selector: 'br', format: 'lineBreak' },\n    { selector: 'div', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'footer', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'form', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'h1', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h2', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h3', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h4', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h5', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h6', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'header', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'hr',\n      format: 'horizontalLine',\n      options: { leadingLineBreaks: 2, length: undefined, trailingLineBreaks: 2 }\n    },\n    {\n      selector: 'img',\n      format: 'image',\n      options: { baseUrl: null, linkBrackets: ['[', ']'] }\n    },\n    { selector: 'main', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'nav', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'ol',\n      format: 'orderedList',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'p', format: 'paragraph', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'pre', format: 'pre', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'section', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'table',\n      format: 'table',\n      options: {\n        colSpacing: 3,\n        leadingLineBreaks: 2,\n        maxColumnWidth: 60,\n        rowSpacing: 0,\n        trailingLineBreaks: 2,\n        uppercaseHeaderCells: true\n      }\n    },\n    {\n      selector: 'ul',\n      format: 'unorderedList',\n      options: { itemPrefix: ' * ', leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'wbr', format: 'wbr' },\n  ],\n  tables: [], // deprecated\n  whitespaceCharacters: ' \\t\\r\\n\\f\\u200b',\n  wordwrap: 80\n};\n\nconst concatMerge = (acc, src, options) => [...acc, ...src];\nconst overwriteMerge = (acc, src, options) => [...src];\nconst selectorsMerge = (acc, src, options) => (\n  (acc.some(s => typeof s === 'object'))\n    ? concatMerge(acc, src) // selectors\n    : overwriteMerge(acc, src) // baseElements.selectors\n);\n\n/**\n * Preprocess options, compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options.\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile (options = {}) {\n  options = deepmerge__WEBPACK_IMPORTED_MODULE_2___default()(\n    DEFAULT_OPTIONS,\n    options,\n    {\n      arrayMerge: overwriteMerge,\n      customMerge: (key) => ((key === 'selectors') ? selectorsMerge : undefined)\n    }\n  );\n  options.formatters = Object.assign({}, genericFormatters, textFormatters, options.formatters);\n  options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n\n  handleDeprecatedOptions(options);\n\n  return compile$1(options);\n}\n\n/**\n * Convert given HTML content to plain text string.\n *\n * @param   { string }  html           HTML content to convert.\n * @param   { Options } [options = {}] HtmlToText options.\n * @param   { any }     [metadata]     Optional metadata for HTML document, for use in formatters.\n * @returns { string }                 Plain text string.\n * @static\n *\n * @example\n * const { convert } = require('html-to-text');\n * const text = convert('<h1>Hello World</h1>', {\n *   wordwrap: 130\n * });\n * console.log(text); // HELLO WORLD\n */\nfunction convert (html, options = {}, metadata = undefined) {\n  return compile(options)(html, metadata);\n}\n\n/**\n * Map previously existing and now deprecated options to the new options layout.\n * This is a subject for cleanup in major releases.\n *\n * @param { Options } options HtmlToText options.\n */\nfunction handleDeprecatedOptions (options) {\n  if (options.tags) {\n    const tagDefinitions = Object.entries(options.tags).map(\n      ([selector, definition]) => ({ ...definition, selector: selector || '*' })\n    );\n    options.selectors.push(...tagDefinitions);\n    options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n  }\n\n  function set (obj, path, value) {\n    const valueKey = path.pop();\n    for (const key of path) {\n      let nested = obj[key];\n      if (!nested) {\n        nested = {};\n        obj[key] = nested;\n      }\n      obj = nested;\n    }\n    obj[valueKey] = value;\n  }\n\n  if (options['baseElement']) {\n    const baseElement = options['baseElement'];\n    set(\n      options,\n      ['baseElements', 'selectors'],\n      (Array.isArray(baseElement) ? baseElement : [baseElement])\n    );\n  }\n  if (options['returnDomByDefault'] !== undefined) {\n    set(options, ['baseElements', 'returnDomByDefault'], options['returnDomByDefault']);\n  }\n\n  for (const definition of options.selectors) {\n    if (definition.format === 'anchor' && get(definition, ['options', 'noLinkBrackets'])) {\n      set(definition, ['options', 'linkBrackets'], false);\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/html-to-text@9.0.5/node_modules/html-to-text/lib/html-to-text.mjs\n");

/***/ })

};
;