/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?2f02\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\")), \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX0BvcGVudGVsZW1ldHJ5K2FwaUAxLjkuMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGRGF0YSUyRlZpYmUtQ29kZSUyRnRlc3Qtd2ViLTEuMCUyRm5vZGVfbW9kdWxlcyUyRi5wbnBtJTJGbmV4dCU0MDE0LjIuMzBfJTQwb3BlbnRlbGVtZXRyeSUyQmFwaSU0MDEuOS4wX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhXQUFvTSIsInNvdXJjZXMiOlsid2VicGFjazovL3R1Y3NlbmJlcmctd2Vic2l0ZS8/MTI2NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9EYXRhL1ZpYmUtQ29kZS90ZXN0LXdlYi0xLjAvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9Ab3BlbnRlbGVtZXRyeSthcGlAMS45LjBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40opentelemetry%2Bapi%401.9.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX0BvcGVudGVsZW1ldHJ5K2FwaUAxLjkuMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGRGF0YSUyRlZpYmUtQ29kZSUyRnRlc3Qtd2ViLTEuMCUyRnNyYyUyRmFwcCUyRmVycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXlGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHVjc2VuYmVyZy13ZWJzaXRlLz8xZWM0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0RhdGEvVmliZS1Db2RlL3Rlc3Qtd2ViLTEuMC9zcmMvYXBwL2Vycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,Mail,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,Mail,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,Mail,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,Mail,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 记录错误到控制台（生产环境中应该发送到错误监控服务）\n        console.error(\"Application error:\", error);\n    // 可以在这里集成 Sentry 或其他错误监控服务\n    // Sentry.captureException(error)\n    }, [\n        error\n    ]);\n    const isDevelopment = \"development\" === \"development\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center justify-center w-24 h-24 bg-red-100 rounded-full mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-12 w-12 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-red-600 mx-auto rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Something went wrong\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-6\",\n                            children: \"We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                isDevelopment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-6 mb-8 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold text-red-800 mb-3\",\n                            children: \"Development Error Details\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-red-700\",\n                                            children: \"Error:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 font-mono text-sm mt-1\",\n                                            children: error.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                error.digest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-red-700\",\n                                            children: \"Digest:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 font-mono text-sm mt-1\",\n                                            children: error.digest\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 17\n                                }, this),\n                                error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-red-700\",\n                                            children: \"Stack Trace:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"text-red-600 font-mono text-xs mt-1 overflow-x-auto whitespace-pre-wrap\",\n                                            children: error.stack\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"What can you do?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Try again\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"The issue might be temporary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Go to homepage\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Start fresh from our main page\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Report the issue\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Help us improve by reporting this error\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Check status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Visit our status page for updates\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: reset,\n                                className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Try Again\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"inline-flex items-center px-6 py-3 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Go Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/contact\",\n                                className: \"inline-flex items-center px-6 py-3 bg-white text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_Mail_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Contact Support\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                error.digest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold text-gray-700 mb-2\",\n                            children: \"Error Reference\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-600 font-mono\",\n                            children: [\n                                \"Error ID: \",\n                                error.digest\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: \"Please include this ID when contacting support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-8 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Need immediate assistance? Contact us at\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            \"or call our emergency hotline\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"tel:******-FLOOD-PROTECTION\",\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"******-FLOOD-PROTECTION\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"80e7294b7f81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHVjc2VuYmVyZy13ZWJzaXRlLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8wMzNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODBlNzI5NGI3ZjgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPPORTED_LOCALES: () => (/* binding */ SUPPORTED_LOCALES),\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n// 支持的语言配置\nconst SUPPORTED_LOCALES = {\n    \"zh-CN\": {\n        name: \"中文\",\n        nativeName: \"简体中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        dir: \"ltr\"\n    },\n    \"en\": {\n        name: \"English\",\n        nativeName: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        dir: \"ltr\"\n    },\n    \"ja\": {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        dir: \"ltr\"\n    },\n    \"es\": {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        dir: \"ltr\"\n    }\n};\n// 生成动态metadata的函数\nfunction generateMetadata(locale = \"zh-CN\") {\n    const localeConfig = SUPPORTED_LOCALES[locale];\n    // 根据语言生成不同的metadata\n    const metadataByLocale = {\n        \"zh-CN\": {\n            title: {\n                default: \"Tucsenberg - 专业防洪设备制造商\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"图森博格(Tucsenberg)成立于2010年，专注于防洪设备研发、设计与制造，为全球用户提供质量过硬的防洪产品系列和综合解决方案。\",\n            keywords: [\n                \"防洪设备\",\n                \"防洪墙\",\n                \"防洪产品\",\n                \"应急防洪\",\n                \"工程防洪\",\n                \"Tucsenberg\",\n                \"图森博格\"\n            ]\n        },\n        \"en\": {\n            title: {\n                default: \"Tucsenberg - Professional Flood Protection Equipment Manufacturer\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"Tucsenberg, established in 2010, specializes in flood protection equipment R&D, design and manufacturing, providing high-quality flood protection products and comprehensive solutions for global users.\",\n            keywords: [\n                \"flood protection\",\n                \"flood barriers\",\n                \"flood control\",\n                \"emergency flood protection\",\n                \"industrial flood protection\",\n                \"Tucsenberg\"\n            ]\n        },\n        \"ja\": {\n            title: {\n                default: \"Tucsenberg - プロフェッショナル洪水防護設備メーカー\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"2010年に設立されたTucsenbergは、洪水防護設備の研究開発、設計、製造を専門とし、世界中のユーザーに高品質な洪水防護製品と包括的なソリューションを提供しています。\",\n            keywords: [\n                \"洪水防護\",\n                \"防水壁\",\n                \"洪水制御\",\n                \"緊急洪水防護\",\n                \"産業洪水防護\",\n                \"Tucsenberg\"\n            ]\n        },\n        \"es\": {\n            title: {\n                default: \"Tucsenberg - Fabricante Profesional de Equipos de Protecci\\xf3n contra Inundaciones\",\n                template: \"%s | Tucsenberg\"\n            },\n            description: \"Tucsenberg, establecida en 2010, se especializa en I+D, dise\\xf1o y fabricaci\\xf3n de equipos de protecci\\xf3n contra inundaciones, proporcionando productos de alta calidad y soluciones integrales para usuarios globales.\",\n            keywords: [\n                \"protecci\\xf3n contra inundaciones\",\n                \"barreras contra inundaciones\",\n                \"control de inundaciones\",\n                \"protecci\\xf3n de emergencia\",\n                \"protecci\\xf3n industrial\",\n                \"Tucsenberg\"\n            ]\n        }\n    };\n    const localizedContent = metadataByLocale[locale];\n    return {\n        ...localizedContent,\n        authors: [\n            {\n                name: \"Tucsenberg\",\n                url: \"https://tucsenberg.com\"\n            }\n        ],\n        creator: \"Tucsenberg\",\n        publisher: \"Tucsenberg\",\n        formatDetection: {\n            email: false,\n            address: false,\n            telephone: false\n        },\n        metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"),\n        alternates: {\n            canonical: \"/\",\n            languages: {\n                \"en\": \"/en\",\n                \"zh-CN\": \"/zh-CN\",\n                \"ja\": \"/ja\",\n                \"es\": \"/es\"\n            }\n        },\n        openGraph: {\n            type: \"website\",\n            locale: locale === \"zh-CN\" ? \"zh_CN\" : locale === \"ja\" ? \"ja_JP\" : locale === \"es\" ? \"es_ES\" : \"en_US\",\n            url: \"/\",\n            title: localizedContent.title.default,\n            description: localizedContent.description,\n            siteName: \"Tucsenberg\",\n            images: [\n                {\n                    url: \"/images/og-image.jpg\",\n                    width: 1200,\n                    height: 630,\n                    alt: locale === \"zh-CN\" ? \"Tucsenberg 防洪设备\" : locale === \"ja\" ? \"Tucsenberg 洪水防護設備\" : locale === \"es\" ? \"Tucsenberg Equipos de Protecci\\xf3n contra Inundaciones\" : \"Tucsenberg Flood Protection Equipment\"\n                }\n            ]\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: localizedContent.title.default,\n            description: localizedContent.description,\n            images: [\n                \"/images/og-image.jpg\"\n            ]\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        },\n        verification: {\n            google: \"your-google-verification-code\",\n            yandex: \"your-yandex-verification-code\",\n            yahoo: \"your-yahoo-verification-code\"\n        }\n    };\n}\n// 默认导出metadata（用于根布局）\nconst metadata = generateMetadata(\"zh-CN\");\n// 获取当前语言的函数\nfunction getCurrentLocale() {\n    // 在服务端，我们需要从URL或其他方式获取语言\n    // 这里先返回默认语言，实际的语言检测由中间件处理\n    return \"zh-CN\";\n}\nfunction RootLayout({ children, params }) {\n    // 从URL参数或其他方式获取当前语言\n    const currentLocale = params?.locale || getCurrentLocale();\n    const localeConfig = SUPPORTED_LOCALES[currentLocale] || SUPPORTED_LOCALES[\"zh-CN\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: currentLocale,\n        dir: localeConfig.dir,\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#2563eb\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    Object.entries(SUPPORTED_LOCALES).map(([locale, config])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"alternate\",\n                            hrefLang: locale === \"zh-CN\" ? \"zh-Hans\" : locale,\n                            href: `${process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"}/${locale}`\n                        }, locale, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"x-default\",\n                        href: `${process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"}/zh-CN`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n}\n// 导出语言配置供其他组件使用\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Mail,Search!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Mail,Search!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Mail,Search!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Home,Mail,Search!=!lucide-react */ \"(rsc)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-8xl md:text-9xl font-bold text-blue-600 mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-1 bg-blue-600 mx-auto rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 mb-6\",\n                            children: \"Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you entered the wrong URL.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-4\",\n                            children: \"What can you do?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Search our site\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 36,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Use our search to find what you're looking for\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Go to homepage\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Start fresh from our main page\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Go back\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Return to the previous page\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Contact us\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Let us know if you need help\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Popular Pages\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/products\",\n                                    className: \"inline-flex items-center px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/about\",\n                                    className: \"inline-flex items-center px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors\",\n                                    children: \"About Us\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/contact\",\n                                    className: \"inline-flex items-center px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Contact\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Search Our Site\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search products, articles...\",\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.history.back(),\n                        className: \"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Home_Mail_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            \"Go back to previous page\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 pt-8 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Still need help? Contact us at\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"mailto:<EMAIL>\",\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"<EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            \"or call\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"tel:******-FLOOD-PROTECTION\",\n                                className: \"text-blue-600 hover:text-blue-800\",\n                                children: \"******-FLOOD-PROTECTION\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTRCO0FBQ2dDO0FBRTdDLFNBQVNLO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FBb0Q7Ozs7OztzQ0FHbkUsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OEJBSWpCLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUFvRDs7Ozs7O3NDQUdsRSw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQTZCOzs7Ozs7Ozs7Ozs7OEJBTzVDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNHOzRCQUFHSCxXQUFVO3NDQUEyQzs7Ozs7O3NDQUd6RCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNMLHNHQUFNQTs0Q0FBQ0ssV0FBVTs7Ozs7O3NEQUNsQiw4REFBQ0Q7OzhEQUNDLDhEQUFDSztvREFBR0osV0FBVTs4REFBNEI7Ozs7Ozs4REFDMUMsOERBQUNFO29EQUFFRixXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUt6Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDTixzR0FBSUE7NENBQUNNLFdBQVU7Ozs7OztzREFDaEIsOERBQUNEOzs4REFDQyw4REFBQ0s7b0RBQUdKLFdBQVU7OERBQTRCOzs7Ozs7OERBQzFDLDhEQUFDRTtvREFBRUYsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLekMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0osc0dBQVNBOzRDQUFDSSxXQUFVOzs7Ozs7c0RBQ3JCLDhEQUFDRDs7OERBQ0MsOERBQUNLO29EQUFHSixXQUFVOzhEQUE0Qjs7Ozs7OzhEQUMxQyw4REFBQ0U7b0RBQUVGLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3pDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNILHNHQUFJQTs0Q0FBQ0csV0FBVTs7Ozs7O3NEQUNoQiw4REFBQ0Q7OzhEQUNDLDhEQUFDSztvREFBR0osV0FBVTs4REFBNEI7Ozs7Ozs4REFDMUMsOERBQUNFO29EQUFFRixXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVM3Qyw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRzs0QkFBR0gsV0FBVTtzQ0FBc0M7Ozs7OztzQ0FHcEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1AsaURBQUlBO29DQUNIWSxNQUFLO29DQUNMTCxXQUFVOztzREFFViw4REFBQ04sc0dBQUlBOzRDQUFDTSxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUduQyw4REFBQ1AsaURBQUlBO29DQUNIWSxNQUFLO29DQUNMTCxXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNQLGlEQUFJQTtvQ0FDSFksTUFBSztvQ0FDTEwsV0FBVTs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDUCxpREFBSUE7b0NBQ0hZLE1BQUs7b0NBQ0xMLFdBQVU7O3NEQUVWLDhEQUFDSCxzR0FBSUE7NENBQUNHLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT3ZDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNJOzRCQUFHSixXQUFVO3NDQUEyQzs7Ozs7O3NDQUd6RCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FDQ0MsTUFBSztvQ0FDTEMsYUFBWTtvQ0FDWlIsV0FBVTs7Ozs7OzhDQUVaLDhEQUFDUztvQ0FBT1QsV0FBVTs4Q0FDaEIsNEVBQUNMLHNHQUFNQTt3Q0FBQ0ssV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTXhCLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ1M7d0JBQ0NDLFNBQVMsSUFBTUMsT0FBT0MsT0FBTyxDQUFDQyxJQUFJO3dCQUNsQ2IsV0FBVTs7MENBRVYsOERBQUNKLHNHQUFTQTtnQ0FBQ0ksV0FBVTs7Ozs7OzRCQUFpQjs7Ozs7Ozs7Ozs7OzhCQU0xQyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFFRixXQUFVOzs0QkFBd0I7NEJBQ0o7MENBQy9CLDhEQUFDYztnQ0FDQ1QsTUFBSztnQ0FDTEwsV0FBVTswQ0FDWDs7Ozs7OzRCQUdBOzRCQUFJOzRCQUFROzBDQUNiLDhEQUFDYztnQ0FDQ1QsTUFBSztnQ0FDTEwsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHVjc2VuYmVyZy13ZWJzaXRlLy4vc3JjL2FwcC9ub3QtZm91bmQudHN4P2NhZTIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgSG9tZSwgU2VhcmNoLCBBcnJvd0xlZnQsIE1haWwgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB0by1pbmRpZ28tMTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctMnhsIG14LWF1dG8gdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgey8qIDQwNCDlm77moIcgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC04eGwgbWQ6dGV4dC05eGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDAgbWItNFwiPlxuICAgICAgICAgICAgNDA0XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTI0IGgtMSBiZy1ibHVlLTYwMCBteC1hdXRvIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog6ZSZ6K+v5L+h5oGvICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgUGFnZSBOb3QgRm91bmRcbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgICBTb3JyeSwgd2UgY291bGRuJ3QgZmluZCB0aGUgcGFnZSB5b3UncmUgbG9va2luZyBmb3IuIFxuICAgICAgICAgICAgVGhlIHBhZ2UgbWlnaHQgaGF2ZSBiZWVuIG1vdmVkLCBkZWxldGVkLCBvciB5b3UgZW50ZXJlZCB0aGUgd3JvbmcgVVJMLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOaQnOe0ouW7uuiuriAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTYgbWItOFwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICBXaGF0IGNhbiB5b3UgZG8/XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTQgdGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwIG10LTEgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5TZWFyY2ggb3VyIHNpdGU8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgVXNlIG91ciBzZWFyY2ggdG8gZmluZCB3aGF0IHlvdSdyZSBsb29raW5nIGZvclxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPEhvbWUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwIG10LTEgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5HbyB0byBob21lcGFnZTwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICBTdGFydCBmcmVzaCBmcm9tIG91ciBtYWluIHBhZ2VcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwIG10LTEgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5HbyBiYWNrPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIFJldHVybiB0byB0aGUgcHJldmlvdXMgcGFnZVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwIG10LTEgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5Db250YWN0IHVzPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIExldCB1cyBrbm93IGlmIHlvdSBuZWVkIGhlbHBcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDlv6vpgJ/pk77mjqUgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00IG1iLThcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgIFBvcHVsYXIgUGFnZXNcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAganVzdGlmeS1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8SG9tZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBIb21lXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL3Byb2R1Y3RzXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy13aGl0ZSB0ZXh0LWJsdWUtNjAwIGJvcmRlciBib3JkZXItYmx1ZS02MDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUHJvZHVjdHNcbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvYWJvdXRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJnLXdoaXRlIHRleHQtYmx1ZS02MDAgYm9yZGVyIGJvcmRlci1ibHVlLTYwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBBYm91dCBVc1xuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgaHJlZj1cIi9jb250YWN0XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy13aGl0ZSB0ZXh0LWJsdWUtNjAwIGJvcmRlciBib3JkZXItYmx1ZS02MDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgQ29udGFjdFxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog5pCc57Si5qGGICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICBTZWFyY2ggT3VyIFNpdGVcbiAgICAgICAgICA8L2gzPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFwiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggcHJvZHVjdHMsIGFydGljbGVzLi4uXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInB4LTYgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtci1sZyBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDov5Tlm57mjInpkq4gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOFwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5oaXN0b3J5LmJhY2soKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgIEdvIGJhY2sgdG8gcHJldmlvdXMgcGFnZVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog6IGU57O75L+h5oGvICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEyIHB0LTggYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICBTdGlsbCBuZWVkIGhlbHA/IENvbnRhY3QgdXMgYXR7JyAnfVxuICAgICAgICAgICAgPGEgXG4gICAgICAgICAgICAgIGhyZWY9XCJtYWlsdG86aW5mb0B0dWNzZW5iZXJnLmNvbVwiIFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBpbmZvQHR1Y3NlbmJlcmcuY29tXG4gICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICB7JyAnfW9yIGNhbGx7JyAnfVxuICAgICAgICAgICAgPGEgXG4gICAgICAgICAgICAgIGhyZWY9XCJ0ZWw6KzEtODAwLUZMT09ELVBST1RFQ1RJT05cIiBcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgKzEtODAwLUZMT09ELVBST1RFQ1RJT05cbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwiSG9tZSIsIlNlYXJjaCIsIkFycm93TGVmdCIsIk1haWwiLCJOb3RGb3VuZCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsImgyIiwiaDMiLCJocmVmIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJidXR0b24iLCJvbkNsaWNrIiwid2luZG93IiwiaGlzdG9yeSIsImJhY2siLCJhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootPage)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/navigation.react-server.js\");\n\nfunction RootPage() {\n    // 重定向到默认语言\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect)(\"/zh-CN\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDO0FBRTNCLFNBQVNDO0lBQ3RCLFdBQVc7SUFDWEQseURBQVFBLENBQUM7QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL3R1Y3NlbmJlcmctd2Vic2l0ZS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyByZWRpcmVjdCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdFBhZ2UoKSB7XG4gIC8vIOmHjeWumuWQkeWIsOm7mOiupOivreiogFxuICByZWRpcmVjdCgnL3poLUNOJylcbn1cbiJdLCJuYW1lcyI6WyJyZWRpcmVjdCIsIlJvb3RQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/lucide-react@0.303.0_react@18.3.1","vendor-chunks/@swc+helpers@0.5.5"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();