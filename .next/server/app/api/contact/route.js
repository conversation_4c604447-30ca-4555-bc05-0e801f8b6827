"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contact/route";
exports.ids = ["app/api/contact/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_Data_Vibe_Code_test_web_1_0_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contact/route.ts */ \"(rsc)/./src/app/api/contact/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contact/route\",\n        pathname: \"/api/contact\",\n        filename: \"route\",\n        bundlePath: \"app/api/contact/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/contact/route.ts\",\n    nextConfigOutput,\n    userland: _Users_Data_Vibe_Code_test_web_1_0_src_app_api_contact_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/contact/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX0BvcGVudGVsZW1ldHJ5K2FwaUAxLjkuMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZjb250YWN0JTJGcm91dGUmcGFnZT0lMkZhcGklMkZjb250YWN0JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY29udGFjdCUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRkRhdGElMkZWaWJlLUNvZGUlMkZ0ZXN0LXdlYi0xLjAlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGRGF0YSUyRlZpYmUtQ29kZSUyRnRlc3Qtd2ViLTEuMCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD1zdGFuZGFsb25lJnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ2U7QUFDNUY7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90dWNzZW5iZXJnLXdlYnNpdGUvP2I2MzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL1VzZXJzL0RhdGEvVmliZS1Db2RlL3Rlc3Qtd2ViLTEuMC9zcmMvYXBwL2FwaS9jb250YWN0L3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcInN0YW5kYWxvbmVcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY29udGFjdC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2NvbnRhY3RcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2NvbnRhY3Qvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMvRGF0YS9WaWJlLUNvZGUvdGVzdC13ZWItMS4wL3NyYy9hcHAvYXBpL2NvbnRhY3Qvcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2NvbnRhY3Qvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/contact/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/contact/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.25.64/node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _lib_email__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/email */ \"(rsc)/./src/lib/email.ts\");\n\n\n\n// 请求验证模式\nconst contactRequestSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(2).max(100),\n    email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().email(),\n    company: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(100).optional(),\n    phone: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(20).optional(),\n    subject: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(5).max(200),\n    message: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(10).max(2000),\n    productInterest: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional(),\n    preferredContact: zod__WEBPACK_IMPORTED_MODULE_1__.z[\"enum\"]([\n        \"email\",\n        \"phone\"\n    ]),\n    urgency: zod__WEBPACK_IMPORTED_MODULE_1__.z[\"enum\"]([\n        \"low\",\n        \"medium\",\n        \"high\"\n    ]),\n    consent: zod__WEBPACK_IMPORTED_MODULE_1__.z.boolean().refine((val)=>val === true),\n    locale: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().default(\"en\")\n});\n// 速率限制存储（生产环境应使用 Redis 或数据库）\nconst rateLimitMap = new Map();\n/**\n * 简单的速率限制检查\n */ function checkRateLimit(ip) {\n    const now = Date.now();\n    const windowMs = 15 * 60 * 1000 // 15 分钟\n    ;\n    const maxRequests = 5 // 每15分钟最多5次请求\n    ;\n    const record = rateLimitMap.get(ip);\n    if (!record || now > record.resetTime) {\n        rateLimitMap.set(ip, {\n            count: 1,\n            resetTime: now + windowMs\n        });\n        return true;\n    }\n    if (record.count >= maxRequests) {\n        return false;\n    }\n    record.count++;\n    return true;\n}\n/**\n * 获取客户端 IP 地址\n */ function getClientIP(request) {\n    const forwarded = request.headers.get(\"x-forwarded-for\");\n    const realIP = request.headers.get(\"x-real-ip\");\n    if (forwarded) {\n        return forwarded.split(\",\")[0].trim();\n    }\n    if (realIP) {\n        return realIP;\n    }\n    return \"unknown\";\n}\n/**\n * 清理和验证输入数据\n */ function sanitizeInput(data) {\n    return {\n        name: data.name.trim(),\n        email: data.email.trim().toLowerCase(),\n        company: data.company?.trim() || undefined,\n        phone: data.phone?.trim() || undefined,\n        subject: data.subject.trim(),\n        message: data.message.trim(),\n        productInterest: data.productInterest || [],\n        preferredContact: data.preferredContact,\n        urgency: data.urgency,\n        consent: data.consent\n    };\n}\n/**\n * POST /api/contact\n * 处理联系表单提交\n */ async function POST(request) {\n    try {\n        // 获取客户端信息\n        const clientIP = getClientIP(request);\n        const userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        // 速率限制检查\n        if (!checkRateLimit(clientIP)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Too many requests. Please try again later.\",\n                error: \"RATE_LIMIT_EXCEEDED\"\n            }, {\n                status: 429\n            });\n        }\n        // 解析请求体\n        let body;\n        try {\n            body = await request.json();\n        } catch (error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Invalid JSON in request body\",\n                error: \"INVALID_JSON\"\n            }, {\n                status: 400\n            });\n        }\n        // 验证输入数据\n        let validatedData;\n        try {\n            validatedData = contactRequestSchema.parse(body);\n        } catch (error) {\n            if (error instanceof zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodError) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    message: \"Validation failed\",\n                    error: error.errors.map((e)=>`${e.path.join(\".\")}: ${e.message}`).join(\", \")\n                }, {\n                    status: 400\n                });\n            }\n            throw error;\n        }\n        // 清理输入数据\n        const sanitizedData = sanitizeInput(validatedData);\n        // 额外验证：如果选择电话联系，必须提供电话号码\n        if (sanitizedData.preferredContact === \"phone\" && !sanitizedData.phone) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Phone number is required when phone is the preferred contact method\",\n                error: \"PHONE_REQUIRED\"\n            }, {\n                status: 400\n            });\n        }\n        // 准备邮件数据\n        const emailData = {\n            ...sanitizedData,\n            submittedAt: new Date().toISOString(),\n            userAgent,\n            ipAddress: clientIP,\n            locale: validatedData.locale\n        };\n        // 发送邮件\n        const emailResult = await (0,_lib_email__WEBPACK_IMPORTED_MODULE_2__.sendContactEmail)(emailData);\n        if (!emailResult.success) {\n            console.error(\"Failed to send contact email:\", emailResult.error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: \"Failed to send email. Please try again later.\",\n                error: \"EMAIL_SEND_FAILED\"\n            }, {\n                status: 500\n            });\n        }\n        // 成功响应\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Your message has been sent successfully. We will get back to you soon.\",\n            id: emailResult.id\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(\"Contact form API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"An unexpected error occurred. Please try again later.\",\n            error: \"INTERNAL_SERVER_ERROR\"\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * GET /api/contact\n * 返回联系表单配置信息（可选）\n */ async function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Contact form API is working\",\n        rateLimit: {\n            windowMs: 15 * 60 * 1000,\n            maxRequests: 5\n        },\n        supportedLocales: [\n            \"en\",\n            \"zh-CN\",\n            \"ja\",\n            \"es\"\n        ],\n        requiredFields: [\n            \"name\",\n            \"email\",\n            \"subject\",\n            \"message\",\n            \"preferredContact\",\n            \"urgency\",\n            \"consent\"\n        ]\n    });\n}\n/**\n * OPTIONS /api/contact\n * CORS 预检请求处理\n */ async function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, GET, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jb250YWN0L3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1RDtBQUNoQztBQUN1QjtBQUc5QyxTQUFTO0FBQ1QsTUFBTUcsdUJBQXVCRix5Q0FBUSxDQUFDO0lBQ3BDSSxNQUFNSix5Q0FBUSxHQUFHTSxHQUFHLENBQUMsR0FBR0MsR0FBRyxDQUFDO0lBQzVCQyxPQUFPUix5Q0FBUSxHQUFHUSxLQUFLO0lBQ3ZCQyxTQUFTVCx5Q0FBUSxHQUFHTyxHQUFHLENBQUMsS0FBS0csUUFBUTtJQUNyQ0MsT0FBT1gseUNBQVEsR0FBR08sR0FBRyxDQUFDLElBQUlHLFFBQVE7SUFDbENFLFNBQVNaLHlDQUFRLEdBQUdNLEdBQUcsQ0FBQyxHQUFHQyxHQUFHLENBQUM7SUFDL0JNLFNBQVNiLHlDQUFRLEdBQUdNLEdBQUcsQ0FBQyxJQUFJQyxHQUFHLENBQUM7SUFDaENPLGlCQUFpQmQsd0NBQU8sQ0FBQ0EseUNBQVEsSUFBSVUsUUFBUTtJQUM3Q00sa0JBQWtCaEIsMENBQU0sQ0FBQztRQUFDO1FBQVM7S0FBUTtJQUMzQ2tCLFNBQVNsQiwwQ0FBTSxDQUFDO1FBQUM7UUFBTztRQUFVO0tBQU87SUFDekNtQixTQUFTbkIsMENBQVMsR0FBR3FCLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsUUFBUTtJQUMzQ0MsUUFBUXZCLHlDQUFRLEdBQUd3QixPQUFPLENBQUM7QUFDN0I7QUFFQSw2QkFBNkI7QUFDN0IsTUFBTUMsZUFBZSxJQUFJQztBQUV6Qjs7Q0FFQyxHQUNELFNBQVNDLGVBQWVDLEVBQVU7SUFDaEMsTUFBTUMsTUFBTUMsS0FBS0QsR0FBRztJQUNwQixNQUFNRSxXQUFXLEtBQUssS0FBSyxLQUFLLFFBQVE7O0lBQ3hDLE1BQU1DLGNBQWMsRUFBRSxjQUFjOztJQUVwQyxNQUFNQyxTQUFTUixhQUFhUyxHQUFHLENBQUNOO0lBRWhDLElBQUksQ0FBQ0ssVUFBVUosTUFBTUksT0FBT0UsU0FBUyxFQUFFO1FBQ3JDVixhQUFhVyxHQUFHLENBQUNSLElBQUk7WUFBRVMsT0FBTztZQUFHRixXQUFXTixNQUFNRTtRQUFTO1FBQzNELE9BQU87SUFDVDtJQUVBLElBQUlFLE9BQU9JLEtBQUssSUFBSUwsYUFBYTtRQUMvQixPQUFPO0lBQ1Q7SUFFQUMsT0FBT0ksS0FBSztJQUNaLE9BQU87QUFDVDtBQUVBOztDQUVDLEdBQ0QsU0FBU0MsWUFBWUMsT0FBb0I7SUFDdkMsTUFBTUMsWUFBWUQsUUFBUUUsT0FBTyxDQUFDUCxHQUFHLENBQUM7SUFDdEMsTUFBTVEsU0FBU0gsUUFBUUUsT0FBTyxDQUFDUCxHQUFHLENBQUM7SUFFbkMsSUFBSU0sV0FBVztRQUNiLE9BQU9BLFVBQVVHLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDQyxJQUFJO0lBQ3JDO0lBRUEsSUFBSUYsUUFBUTtRQUNWLE9BQU9BO0lBQ1Q7SUFFQSxPQUFPO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNELFNBQVNHLGNBQWNDLElBQVM7SUFDOUIsT0FBTztRQUNMMUMsTUFBTTBDLEtBQUsxQyxJQUFJLENBQUN3QyxJQUFJO1FBQ3BCcEMsT0FBT3NDLEtBQUt0QyxLQUFLLENBQUNvQyxJQUFJLEdBQUdHLFdBQVc7UUFDcEN0QyxTQUFTcUMsS0FBS3JDLE9BQU8sRUFBRW1DLFVBQVVJO1FBQ2pDckMsT0FBT21DLEtBQUtuQyxLQUFLLEVBQUVpQyxVQUFVSTtRQUM3QnBDLFNBQVNrQyxLQUFLbEMsT0FBTyxDQUFDZ0MsSUFBSTtRQUMxQi9CLFNBQVNpQyxLQUFLakMsT0FBTyxDQUFDK0IsSUFBSTtRQUMxQjlCLGlCQUFpQmdDLEtBQUtoQyxlQUFlLElBQUksRUFBRTtRQUMzQ0Usa0JBQWtCOEIsS0FBSzlCLGdCQUFnQjtRQUN2Q0UsU0FBUzRCLEtBQUs1QixPQUFPO1FBQ3JCQyxTQUFTMkIsS0FBSzNCLE9BQU87SUFDdkI7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWU4QixLQUFLVixPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsVUFBVTtRQUNWLE1BQU1XLFdBQVdaLFlBQVlDO1FBQzdCLE1BQU1ZLFlBQVlaLFFBQVFFLE9BQU8sQ0FBQ1AsR0FBRyxDQUFDLGlCQUFpQjtRQUV2RCxTQUFTO1FBQ1QsSUFBSSxDQUFDUCxlQUFldUIsV0FBVztZQUM3QixPQUFPbkQscURBQVlBLENBQUNxRCxJQUFJLENBQ3RCO2dCQUNFQyxTQUFTO2dCQUNUeEMsU0FBUztnQkFDVHlDLE9BQU87WUFDVCxHQUNBO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxRQUFRO1FBQ1IsSUFBSUM7UUFDSixJQUFJO1lBQ0ZBLE9BQU8sTUFBTWpCLFFBQVFhLElBQUk7UUFDM0IsRUFBRSxPQUFPRSxPQUFPO1lBQ2QsT0FBT3ZELHFEQUFZQSxDQUFDcUQsSUFBSSxDQUN0QjtnQkFDRUMsU0FBUztnQkFDVHhDLFNBQVM7Z0JBQ1R5QyxPQUFPO1lBQ1QsR0FDQTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsU0FBUztRQUNULElBQUlFO1FBQ0osSUFBSTtZQUNGQSxnQkFBZ0J2RCxxQkFBcUJ3RCxLQUFLLENBQUNGO1FBQzdDLEVBQUUsT0FBT0YsT0FBTztZQUNkLElBQUlBLGlCQUFpQnRELDJDQUFVLEVBQUU7Z0JBQy9CLE9BQU9ELHFEQUFZQSxDQUFDcUQsSUFBSSxDQUN0QjtvQkFDRUMsU0FBUztvQkFDVHhDLFNBQVM7b0JBQ1R5QyxPQUFPQSxNQUFNTSxNQUFNLENBQUNDLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBSyxDQUFDLEVBQUVBLEVBQUVDLElBQUksQ0FBQ0MsSUFBSSxDQUFDLEtBQUssRUFBRSxFQUFFRixFQUFFakQsT0FBTyxDQUFDLENBQUMsRUFBRW1ELElBQUksQ0FBQztnQkFDekUsR0FDQTtvQkFBRVQsUUFBUTtnQkFBSTtZQUVsQjtZQUNBLE1BQU1EO1FBQ1I7UUFFQSxTQUFTO1FBQ1QsTUFBTVcsZ0JBQWdCcEIsY0FBY1k7UUFFcEMseUJBQXlCO1FBQ3pCLElBQUlRLGNBQWNqRCxnQkFBZ0IsS0FBSyxXQUFXLENBQUNpRCxjQUFjdEQsS0FBSyxFQUFFO1lBQ3RFLE9BQU9aLHFEQUFZQSxDQUFDcUQsSUFBSSxDQUN0QjtnQkFDRUMsU0FBUztnQkFDVHhDLFNBQVM7Z0JBQ1R5QyxPQUFPO1lBQ1QsR0FDQTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsU0FBUztRQUNULE1BQU1XLFlBQThCO1lBQ2xDLEdBQUdELGFBQWE7WUFDaEJFLGFBQWEsSUFBSXJDLE9BQU9zQyxXQUFXO1lBQ25DakI7WUFDQWtCLFdBQVduQjtZQUNYM0IsUUFBUWtDLGNBQWNsQyxNQUFNO1FBQzlCO1FBRUEsT0FBTztRQUNQLE1BQU0rQyxjQUFjLE1BQU1yRSw0REFBZ0JBLENBQUNpRTtRQUUzQyxJQUFJLENBQUNJLFlBQVlqQixPQUFPLEVBQUU7WUFDeEJrQixRQUFRakIsS0FBSyxDQUFDLGlDQUFpQ2dCLFlBQVloQixLQUFLO1lBQ2hFLE9BQU92RCxxREFBWUEsQ0FBQ3FELElBQUksQ0FDdEI7Z0JBQ0VDLFNBQVM7Z0JBQ1R4QyxTQUFTO2dCQUNUeUMsT0FBTztZQUNULEdBQ0E7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE9BQU87UUFDUCxPQUFPeEQscURBQVlBLENBQUNxRCxJQUFJLENBQ3RCO1lBQ0VDLFNBQVM7WUFDVHhDLFNBQVM7WUFDVDJELElBQUlGLFlBQVlFLEVBQUU7UUFDcEIsR0FDQTtZQUFFakIsUUFBUTtRQUFJO0lBR2xCLEVBQUUsT0FBT0QsT0FBTztRQUNkaUIsUUFBUWpCLEtBQUssQ0FBQywyQkFBMkJBO1FBRXpDLE9BQU92RCxxREFBWUEsQ0FBQ3FELElBQUksQ0FDdEI7WUFDRUMsU0FBUztZQUNUeEMsU0FBUztZQUNUeUMsT0FBTztRQUNULEdBQ0E7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxlQUFla0I7SUFDcEIsT0FBTzFFLHFEQUFZQSxDQUFDcUQsSUFBSSxDQUFDO1FBQ3ZCdkMsU0FBUztRQUNUNkQsV0FBVztZQUNUM0MsVUFBVSxLQUFLLEtBQUs7WUFDcEJDLGFBQWE7UUFDZjtRQUNBMkMsa0JBQWtCO1lBQUM7WUFBTTtZQUFTO1lBQU07U0FBSztRQUM3Q0MsZ0JBQWdCO1lBQUM7WUFBUTtZQUFTO1lBQVc7WUFBVztZQUFvQjtZQUFXO1NBQVU7SUFDbkc7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVDO0lBQ3BCLE9BQU8sSUFBSTlFLHFEQUFZQSxDQUFDLE1BQU07UUFDNUJ3RCxRQUFRO1FBQ1JkLFNBQVM7WUFDUCwrQkFBK0I7WUFDL0IsZ0NBQWdDO1lBQ2hDLGdDQUFnQztRQUNsQztJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90dWNzZW5iZXJnLXdlYnNpdGUvLi9zcmMvYXBwL2FwaS9jb250YWN0L3JvdXRlLnRzPzAyMGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCdcbmltcG9ydCB7IHNlbmRDb250YWN0RW1haWwgfSBmcm9tICdAL2xpYi9lbWFpbCdcbmltcG9ydCB0eXBlIHsgQ29udGFjdEZvcm1EYXRhLCBDb250YWN0RW1haWxEYXRhLCBDb250YWN0U3VibWlzc2lvblJlc3BvbnNlIH0gZnJvbSAnQC90eXBlcy9jb250YWN0J1xuXG4vLyDor7fmsYLpqozor4HmqKHlvI9cbmNvbnN0IGNvbnRhY3RSZXF1ZXN0U2NoZW1hID0gei5vYmplY3Qoe1xuICBuYW1lOiB6LnN0cmluZygpLm1pbigyKS5tYXgoMTAwKSxcbiAgZW1haWw6IHouc3RyaW5nKCkuZW1haWwoKSxcbiAgY29tcGFueTogei5zdHJpbmcoKS5tYXgoMTAwKS5vcHRpb25hbCgpLFxuICBwaG9uZTogei5zdHJpbmcoKS5tYXgoMjApLm9wdGlvbmFsKCksXG4gIHN1YmplY3Q6IHouc3RyaW5nKCkubWluKDUpLm1heCgyMDApLFxuICBtZXNzYWdlOiB6LnN0cmluZygpLm1pbigxMCkubWF4KDIwMDApLFxuICBwcm9kdWN0SW50ZXJlc3Q6IHouYXJyYXkoei5zdHJpbmcoKSkub3B0aW9uYWwoKSxcbiAgcHJlZmVycmVkQ29udGFjdDogei5lbnVtKFsnZW1haWwnLCAncGhvbmUnXSksXG4gIHVyZ2VuY3k6IHouZW51bShbJ2xvdycsICdtZWRpdW0nLCAnaGlnaCddKSxcbiAgY29uc2VudDogei5ib29sZWFuKCkucmVmaW5lKHZhbCA9PiB2YWwgPT09IHRydWUpLFxuICBsb2NhbGU6IHouc3RyaW5nKCkuZGVmYXVsdCgnZW4nKVxufSlcblxuLy8g6YCf546H6ZmQ5Yi25a2Y5YKo77yI55Sf5Lqn546v5aKD5bqU5L2/55SoIFJlZGlzIOaIluaVsOaNruW6k++8iVxuY29uc3QgcmF0ZUxpbWl0TWFwID0gbmV3IE1hcDxzdHJpbmcsIHsgY291bnQ6IG51bWJlcjsgcmVzZXRUaW1lOiBudW1iZXIgfT4oKVxuXG4vKipcbiAqIOeugOWNleeahOmAn+eOh+mZkOWItuajgOafpVxuICovXG5mdW5jdGlvbiBjaGVja1JhdGVMaW1pdChpcDogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGNvbnN0IG5vdyA9IERhdGUubm93KClcbiAgY29uc3Qgd2luZG93TXMgPSAxNSAqIDYwICogMTAwMCAvLyAxNSDliIbpkp9cbiAgY29uc3QgbWF4UmVxdWVzdHMgPSA1IC8vIOavjzE15YiG6ZKf5pyA5aSaNeasoeivt+axglxuXG4gIGNvbnN0IHJlY29yZCA9IHJhdGVMaW1pdE1hcC5nZXQoaXApXG4gIFxuICBpZiAoIXJlY29yZCB8fCBub3cgPiByZWNvcmQucmVzZXRUaW1lKSB7XG4gICAgcmF0ZUxpbWl0TWFwLnNldChpcCwgeyBjb3VudDogMSwgcmVzZXRUaW1lOiBub3cgKyB3aW5kb3dNcyB9KVxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICBpZiAocmVjb3JkLmNvdW50ID49IG1heFJlcXVlc3RzKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICByZWNvcmQuY291bnQrK1xuICByZXR1cm4gdHJ1ZVxufVxuXG4vKipcbiAqIOiOt+WPluWuouaIt+erryBJUCDlnLDlnYBcbiAqL1xuZnVuY3Rpb24gZ2V0Q2xpZW50SVAocmVxdWVzdDogTmV4dFJlcXVlc3QpOiBzdHJpbmcge1xuICBjb25zdCBmb3J3YXJkZWQgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKVxuICBjb25zdCByZWFsSVAgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LXJlYWwtaXAnKVxuICBcbiAgaWYgKGZvcndhcmRlZCkge1xuICAgIHJldHVybiBmb3J3YXJkZWQuc3BsaXQoJywnKVswXS50cmltKClcbiAgfVxuICBcbiAgaWYgKHJlYWxJUCkge1xuICAgIHJldHVybiByZWFsSVBcbiAgfVxuICBcbiAgcmV0dXJuICd1bmtub3duJ1xufVxuXG4vKipcbiAqIOa4heeQhuWSjOmqjOivgei+k+WFpeaVsOaNrlxuICovXG5mdW5jdGlvbiBzYW5pdGl6ZUlucHV0KGRhdGE6IGFueSk6IENvbnRhY3RGb3JtRGF0YSB7XG4gIHJldHVybiB7XG4gICAgbmFtZTogZGF0YS5uYW1lLnRyaW0oKSxcbiAgICBlbWFpbDogZGF0YS5lbWFpbC50cmltKCkudG9Mb3dlckNhc2UoKSxcbiAgICBjb21wYW55OiBkYXRhLmNvbXBhbnk/LnRyaW0oKSB8fCB1bmRlZmluZWQsXG4gICAgcGhvbmU6IGRhdGEucGhvbmU/LnRyaW0oKSB8fCB1bmRlZmluZWQsXG4gICAgc3ViamVjdDogZGF0YS5zdWJqZWN0LnRyaW0oKSxcbiAgICBtZXNzYWdlOiBkYXRhLm1lc3NhZ2UudHJpbSgpLFxuICAgIHByb2R1Y3RJbnRlcmVzdDogZGF0YS5wcm9kdWN0SW50ZXJlc3QgfHwgW10sXG4gICAgcHJlZmVycmVkQ29udGFjdDogZGF0YS5wcmVmZXJyZWRDb250YWN0LFxuICAgIHVyZ2VuY3k6IGRhdGEudXJnZW5jeSxcbiAgICBjb25zZW50OiBkYXRhLmNvbnNlbnRcbiAgfVxufVxuXG4vKipcbiAqIFBPU1QgL2FwaS9jb250YWN0XG4gKiDlpITnkIbogZTns7vooajljZXmj5DkuqRcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpOiBQcm9taXNlPE5leHRSZXNwb25zZTxDb250YWN0U3VibWlzc2lvblJlc3BvbnNlPj4ge1xuICB0cnkge1xuICAgIC8vIOiOt+WPluWuouaIt+err+S/oeaBr1xuICAgIGNvbnN0IGNsaWVudElQID0gZ2V0Q2xpZW50SVAocmVxdWVzdClcbiAgICBjb25zdCB1c2VyQWdlbnQgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd1c2VyLWFnZW50JykgfHwgJ3Vua25vd24nXG5cbiAgICAvLyDpgJ/njofpmZDliLbmo4Dmn6VcbiAgICBpZiAoIWNoZWNrUmF0ZUxpbWl0KGNsaWVudElQKSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgbWVzc2FnZTogJ1RvbyBtYW55IHJlcXVlc3RzLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicsXG4gICAgICAgICAgZXJyb3I6ICdSQVRFX0xJTUlUX0VYQ0VFREVEJ1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDI5IH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyDop6PmnpDor7fmsYLkvZNcbiAgICBsZXQgYm9keVxuICAgIHRyeSB7XG4gICAgICBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7XG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgbWVzc2FnZTogJ0ludmFsaWQgSlNPTiBpbiByZXF1ZXN0IGJvZHknLFxuICAgICAgICAgIGVycm9yOiAnSU5WQUxJRF9KU09OJ1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyDpqozor4HovpPlhaXmlbDmja5cbiAgICBsZXQgdmFsaWRhdGVkRGF0YVxuICAgIHRyeSB7XG4gICAgICB2YWxpZGF0ZWREYXRhID0gY29udGFjdFJlcXVlc3RTY2hlbWEucGFyc2UoYm9keSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2Ygei5ab2RFcnJvcikge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAge1xuICAgICAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgICAgICBtZXNzYWdlOiAnVmFsaWRhdGlvbiBmYWlsZWQnLFxuICAgICAgICAgICAgZXJyb3I6IGVycm9yLmVycm9ycy5tYXAoZSA9PiBgJHtlLnBhdGguam9pbignLicpfTogJHtlLm1lc3NhZ2V9YCkuam9pbignLCAnKVxuICAgICAgICAgIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICAgIClcbiAgICAgIH1cbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuXG4gICAgLy8g5riF55CG6L6T5YWl5pWw5o2uXG4gICAgY29uc3Qgc2FuaXRpemVkRGF0YSA9IHNhbml0aXplSW5wdXQodmFsaWRhdGVkRGF0YSlcblxuICAgIC8vIOmineWklumqjOivge+8muWmguaenOmAieaLqeeUteivneiBlOezu++8jOW/hemhu+aPkOS+m+eUteivneWPt+eggVxuICAgIGlmIChzYW5pdGl6ZWREYXRhLnByZWZlcnJlZENvbnRhY3QgPT09ICdwaG9uZScgJiYgIXNhbml0aXplZERhdGEucGhvbmUpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIG1lc3NhZ2U6ICdQaG9uZSBudW1iZXIgaXMgcmVxdWlyZWQgd2hlbiBwaG9uZSBpcyB0aGUgcHJlZmVycmVkIGNvbnRhY3QgbWV0aG9kJyxcbiAgICAgICAgICBlcnJvcjogJ1BIT05FX1JFUVVJUkVEJ1xuICAgICAgICB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyDlh4blpIfpgq7ku7bmlbDmja5cbiAgICBjb25zdCBlbWFpbERhdGE6IENvbnRhY3RFbWFpbERhdGEgPSB7XG4gICAgICAuLi5zYW5pdGl6ZWREYXRhLFxuICAgICAgc3VibWl0dGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHVzZXJBZ2VudCxcbiAgICAgIGlwQWRkcmVzczogY2xpZW50SVAsXG4gICAgICBsb2NhbGU6IHZhbGlkYXRlZERhdGEubG9jYWxlXG4gICAgfVxuXG4gICAgLy8g5Y+R6YCB6YKu5Lu2XG4gICAgY29uc3QgZW1haWxSZXN1bHQgPSBhd2FpdCBzZW5kQ29udGFjdEVtYWlsKGVtYWlsRGF0YSlcblxuICAgIGlmICghZW1haWxSZXN1bHQuc3VjY2Vzcykge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNlbmQgY29udGFjdCBlbWFpbDonLCBlbWFpbFJlc3VsdC5lcnJvcilcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIG1lc3NhZ2U6ICdGYWlsZWQgdG8gc2VuZCBlbWFpbC4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nLFxuICAgICAgICAgIGVycm9yOiAnRU1BSUxfU0VORF9GQUlMRUQnXG4gICAgICAgIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIC8vIOaIkOWKn+WTjeW6lFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgbWVzc2FnZTogJ1lvdXIgbWVzc2FnZSBoYXMgYmVlbiBzZW50IHN1Y2Nlc3NmdWxseS4gV2Ugd2lsbCBnZXQgYmFjayB0byB5b3Ugc29vbi4nLFxuICAgICAgICBpZDogZW1haWxSZXN1bHQuaWRcbiAgICAgIH0sXG4gICAgICB7IHN0YXR1czogMjAwIH1cbiAgICApXG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdDb250YWN0IGZvcm0gQVBJIGVycm9yOicsIGVycm9yKVxuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6ICdBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkLiBQbGVhc2UgdHJ5IGFnYWluIGxhdGVyLicsXG4gICAgICAgIGVycm9yOiAnSU5URVJOQUxfU0VSVkVSX0VSUk9SJ1xuICAgICAgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuXG4vKipcbiAqIEdFVCAvYXBpL2NvbnRhY3RcbiAqIOi/lOWbnuiBlOezu+ihqOWNlemFjee9ruS/oeaBr++8iOWPr+mAie+8iVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCk6IFByb21pc2U8TmV4dFJlc3BvbnNlPiB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgbWVzc2FnZTogJ0NvbnRhY3QgZm9ybSBBUEkgaXMgd29ya2luZycsXG4gICAgcmF0ZUxpbWl0OiB7XG4gICAgICB3aW5kb3dNczogMTUgKiA2MCAqIDEwMDAsXG4gICAgICBtYXhSZXF1ZXN0czogNVxuICAgIH0sXG4gICAgc3VwcG9ydGVkTG9jYWxlczogWydlbicsICd6aC1DTicsICdqYScsICdlcyddLFxuICAgIHJlcXVpcmVkRmllbGRzOiBbJ25hbWUnLCAnZW1haWwnLCAnc3ViamVjdCcsICdtZXNzYWdlJywgJ3ByZWZlcnJlZENvbnRhY3QnLCAndXJnZW5jeScsICdjb25zZW50J11cbiAgfSlcbn1cblxuLyoqXG4gKiBPUFRJT05TIC9hcGkvY29udGFjdFxuICogQ09SUyDpooTmo4Dor7fmsYLlpITnkIZcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIE9QVElPTlMoKTogUHJvbWlzZTxOZXh0UmVzcG9uc2U+IHtcbiAgcmV0dXJuIG5ldyBOZXh0UmVzcG9uc2UobnVsbCwge1xuICAgIHN0YXR1czogMjAwLFxuICAgIGhlYWRlcnM6IHtcbiAgICAgICdBY2Nlc3MtQ29udHJvbC1BbGxvdy1PcmlnaW4nOiAnKicsXG4gICAgICAnQWNjZXNzLUNvbnRyb2wtQWxsb3ctTWV0aG9kcyc6ICdQT1NULCBHRVQsIE9QVElPTlMnLFxuICAgICAgJ0FjY2Vzcy1Db250cm9sLUFsbG93LUhlYWRlcnMnOiAnQ29udGVudC1UeXBlJyxcbiAgICB9LFxuICB9KVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInoiLCJzZW5kQ29udGFjdEVtYWlsIiwiY29udGFjdFJlcXVlc3RTY2hlbWEiLCJvYmplY3QiLCJuYW1lIiwic3RyaW5nIiwibWluIiwibWF4IiwiZW1haWwiLCJjb21wYW55Iiwib3B0aW9uYWwiLCJwaG9uZSIsInN1YmplY3QiLCJtZXNzYWdlIiwicHJvZHVjdEludGVyZXN0IiwiYXJyYXkiLCJwcmVmZXJyZWRDb250YWN0IiwiZW51bSIsInVyZ2VuY3kiLCJjb25zZW50IiwiYm9vbGVhbiIsInJlZmluZSIsInZhbCIsImxvY2FsZSIsImRlZmF1bHQiLCJyYXRlTGltaXRNYXAiLCJNYXAiLCJjaGVja1JhdGVMaW1pdCIsImlwIiwibm93IiwiRGF0ZSIsIndpbmRvd01zIiwibWF4UmVxdWVzdHMiLCJyZWNvcmQiLCJnZXQiLCJyZXNldFRpbWUiLCJzZXQiLCJjb3VudCIsImdldENsaWVudElQIiwicmVxdWVzdCIsImZvcndhcmRlZCIsImhlYWRlcnMiLCJyZWFsSVAiLCJzcGxpdCIsInRyaW0iLCJzYW5pdGl6ZUlucHV0IiwiZGF0YSIsInRvTG93ZXJDYXNlIiwidW5kZWZpbmVkIiwiUE9TVCIsImNsaWVudElQIiwidXNlckFnZW50IiwianNvbiIsInN1Y2Nlc3MiLCJlcnJvciIsInN0YXR1cyIsImJvZHkiLCJ2YWxpZGF0ZWREYXRhIiwicGFyc2UiLCJab2RFcnJvciIsImVycm9ycyIsIm1hcCIsImUiLCJwYXRoIiwiam9pbiIsInNhbml0aXplZERhdGEiLCJlbWFpbERhdGEiLCJzdWJtaXR0ZWRBdCIsInRvSVNPU3RyaW5nIiwiaXBBZGRyZXNzIiwiZW1haWxSZXN1bHQiLCJjb25zb2xlIiwiaWQiLCJHRVQiLCJyYXRlTGltaXQiLCJzdXBwb3J0ZWRMb2NhbGVzIiwicmVxdWlyZWRGaWVsZHMiLCJPUFRJT05TIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contact/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email.ts":
/*!**************************!*\
  !*** ./src/lib/email.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendContactEmail: () => (/* binding */ sendContactEmail)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/.pnpm/resend@3.5.0_react-dom@18.3.1_react@18.3.1/node_modules/resend/dist/index.mjs\");\n\n// 初始化 Resend 客户端（如果有 API 密钥）\nconst resend = process.env.RESEND_API_KEY ? new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(process.env.RESEND_API_KEY) : null;\n/**\n * 发送联系表单邮件\n */ async function sendContactEmail(data) {\n    try {\n        // 验证必要的环境变量\n        if (!process.env.RESEND_API_KEY) {\n            console.warn(\"RESEND_API_KEY is not configured - email will be logged instead of sent\");\n            // 在开发环境中，记录邮件内容而不是发送\n            const emailTemplate = generateContactEmailTemplate(data);\n            console.log(\"\\uD83D\\uDCE7 Contact Email (Development Mode):\");\n            console.log(\"To:\", process.env.RESEND_TO_EMAIL || \"<EMAIL>\");\n            console.log(\"Subject:\", emailTemplate.subject);\n            console.log(\"Content:\", emailTemplate.text);\n            return {\n                success: true,\n                id: \"dev-mode-\" + Date.now()\n            };\n        }\n        if (!process.env.RESEND_FROM_EMAIL || !process.env.RESEND_TO_EMAIL) {\n            throw new Error(\"Email addresses are not configured\");\n        }\n        if (!resend) {\n            throw new Error(\"Resend client is not initialized\");\n        }\n        // 生成邮件内容\n        const emailTemplate = generateContactEmailTemplate(data);\n        // 发送邮件\n        const result = await resend.emails.send({\n            from: process.env.RESEND_FROM_EMAIL,\n            to: process.env.RESEND_TO_EMAIL,\n            subject: emailTemplate.subject,\n            html: emailTemplate.html,\n            text: emailTemplate.text,\n            // 添加标签用于分类\n            tags: [\n                {\n                    name: \"type\",\n                    value: \"contact-form\"\n                },\n                {\n                    name: \"urgency\",\n                    value: data.urgency\n                },\n                {\n                    name: \"locale\",\n                    value: data.locale\n                }\n            ]\n        });\n        // 发送确认邮件给用户\n        if (data.preferredContact === \"email\") {\n            await sendConfirmationEmail(data);\n        }\n        return {\n            success: true,\n            id: result.data?.id\n        };\n    } catch (error) {\n        console.error(\"Failed to send contact email:\", error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error occurred\"\n        };\n    }\n}\n/**\n * 发送确认邮件给用户\n */ async function sendConfirmationEmail(data) {\n    try {\n        if (!resend) {\n            console.log(\"\\uD83D\\uDCE7 Confirmation Email (Development Mode):\");\n            console.log(\"To:\", data.email);\n            console.log(\"Subject: Thank you for contacting Tucsenberg\");\n            return;\n        }\n        const confirmationTemplate = generateConfirmationEmailTemplate(data);\n        await resend.emails.send({\n            from: process.env.RESEND_FROM_EMAIL,\n            to: data.email,\n            subject: confirmationTemplate.subject,\n            html: confirmationTemplate.html,\n            text: confirmationTemplate.text,\n            tags: [\n                {\n                    name: \"type\",\n                    value: \"confirmation\"\n                },\n                {\n                    name: \"locale\",\n                    value: data.locale\n                }\n            ]\n        });\n    } catch (error) {\n        console.error(\"Failed to send confirmation email:\", error);\n    // 不抛出错误，因为主邮件已经发送成功\n    }\n}\n/**\n * 生成联系表单邮件模板\n */ function generateContactEmailTemplate(data) {\n    const urgencyEmoji = {\n        low: \"\\uD83D\\uDFE2\",\n        medium: \"\\uD83D\\uDFE1\",\n        high: \"\\uD83D\\uDD34\"\n    };\n    const subject = `${urgencyEmoji[data.urgency]} 新的联系表单提交 - ${data.subject}`;\n    const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>联系表单提交</title>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px 8px 0 0; }\n        .content { background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px; }\n        .field { margin-bottom: 15px; }\n        .label { font-weight: bold; color: #374151; }\n        .value { margin-top: 5px; padding: 8px; background: white; border-radius: 4px; }\n        .urgency-high { border-left: 4px solid #ef4444; }\n        .urgency-medium { border-left: 4px solid #f59e0b; }\n        .urgency-low { border-left: 4px solid #10b981; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🏢 Tucsenberg - 新的联系表单</h1>\n          <p>收到来自网站的新联系请求</p>\n        </div>\n        <div class=\"content urgency-${data.urgency}\">\n          <div class=\"field\">\n            <div class=\"label\">👤 姓名:</div>\n            <div class=\"value\">${data.name}</div>\n          </div>\n          \n          <div class=\"field\">\n            <div class=\"label\">📧 邮箱:</div>\n            <div class=\"value\">${data.email}</div>\n          </div>\n          \n          ${data.company ? `\n          <div class=\"field\">\n            <div class=\"label\">🏢 公司:</div>\n            <div class=\"value\">${data.company}</div>\n          </div>\n          ` : \"\"}\n          \n          ${data.phone ? `\n          <div class=\"field\">\n            <div class=\"label\">📞 电话:</div>\n            <div class=\"value\">${data.phone}</div>\n          </div>\n          ` : \"\"}\n          \n          <div class=\"field\">\n            <div class=\"label\">📋 主题:</div>\n            <div class=\"value\">${data.subject}</div>\n          </div>\n          \n          <div class=\"field\">\n            <div class=\"label\">💬 消息:</div>\n            <div class=\"value\">${data.message.replace(/\\n/g, \"<br>\")}</div>\n          </div>\n          \n          ${data.productInterest && data.productInterest.length > 0 ? `\n          <div class=\"field\">\n            <div class=\"label\">🎯 产品兴趣:</div>\n            <div class=\"value\">${data.productInterest.join(\", \")}</div>\n          </div>\n          ` : \"\"}\n          \n          <div class=\"field\">\n            <div class=\"label\">📞 首选联系方式:</div>\n            <div class=\"value\">${data.preferredContact === \"email\" ? \"邮箱\" : \"电话\"}</div>\n          </div>\n          \n          <div class=\"field\">\n            <div class=\"label\">⚡ 紧急程度:</div>\n            <div class=\"value\">${urgencyEmoji[data.urgency]} ${data.urgency.toUpperCase()}</div>\n          </div>\n          \n          <div class=\"field\">\n            <div class=\"label\">🕒 提交时间:</div>\n            <div class=\"value\">${data.submittedAt}</div>\n          </div>\n          \n          <div class=\"field\">\n            <div class=\"label\">🌐 语言:</div>\n            <div class=\"value\">${data.locale}</div>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n    const text = `\nTucsenberg - 新的联系表单提交\n\n姓名: ${data.name}\n邮箱: ${data.email}\n${data.company ? `公司: ${data.company}` : \"\"}\n${data.phone ? `电话: ${data.phone}` : \"\"}\n主题: ${data.subject}\n消息: ${data.message}\n${data.productInterest ? `产品兴趣: ${data.productInterest.join(\", \")}` : \"\"}\n首选联系方式: ${data.preferredContact === \"email\" ? \"邮箱\" : \"电话\"}\n紧急程度: ${data.urgency.toUpperCase()}\n提交时间: ${data.submittedAt}\n语言: ${data.locale}\n  `;\n    return {\n        to: process.env.RESEND_TO_EMAIL,\n        from: process.env.RESEND_FROM_EMAIL,\n        subject,\n        html,\n        text\n    };\n}\n/**\n * 生成确认邮件模板\n */ function generateConfirmationEmailTemplate(data) {\n    const subject = `感谢您联系 Tucsenberg - 我们已收到您的消息`;\n    const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <title>感谢您的联系</title>\n      <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }\n        .content { background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px; }\n        .footer { text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🏢 Tucsenberg</h1>\n          <p>专业防洪设备制造商</p>\n        </div>\n        <div class=\"content\">\n          <h2>亲爱的 ${data.name}，</h2>\n          <p>感谢您联系 Tucsenberg！我们已经收到您关于\"${data.subject}\"的消息。</p>\n          <p>我们的团队将在 24 小时内回复您的询问。如果您的请求标记为高优先级，我们会更快回复。</p>\n          <p>与此同时，您可以：</p>\n          <ul>\n            <li>浏览我们的<a href=\"${process.env.NEXT_PUBLIC_SITE_URL}/products\">产品目录</a></li>\n            <li>了解我们的<a href=\"${process.env.NEXT_PUBLIC_SITE_URL}/about\">公司历史</a></li>\n            <li>查看<a href=\"${process.env.NEXT_PUBLIC_SITE_URL}/case-studies\">成功案例</a></li>\n          </ul>\n          <p>如果您有紧急需求，请直接致电我们的客服热线。</p>\n          <p>再次感谢您选择 Tucsenberg！</p>\n        </div>\n        <div class=\"footer\">\n          <p>此邮件由系统自动发送，请勿直接回复。</p>\n          <p>© 2024 Tucsenberg. 保留所有权利。</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n    const text = `\n亲爱的 ${data.name}，\n\n感谢您联系 Tucsenberg！我们已经收到您关于\"${data.subject}\"的消息。\n\n我们的团队将在 24 小时内回复您的询问。如果您的请求标记为高优先级，我们会更快回复。\n\n与此同时，您可以访问我们的网站了解更多信息：${process.env.NEXT_PUBLIC_SITE_URL}\n\n再次感谢您选择 Tucsenberg！\n\n---\n此邮件由系统自动发送，请勿直接回复。\n© 2024 Tucsenberg. 保留所有权利。\n  `;\n    return {\n        to: data.email,\n        from: process.env.RESEND_FROM_EMAIL,\n        subject,\n        html,\n        text\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/zod@3.25.64","vendor-chunks/entities@4.5.0","vendor-chunks/domutils@3.2.2","vendor-chunks/js-beautify@1.15.4","vendor-chunks/htmlparser2@8.0.2","vendor-chunks/peberminta@0.9.0","vendor-chunks/domhandler@5.0.3","vendor-chunks/dom-serializer@2.0.0","vendor-chunks/selderee@0.11.0","vendor-chunks/resend@3.5.0_react-dom@18.3.1_react@18.3.1","vendor-chunks/parseley@0.12.1","vendor-chunks/leac@0.6.0","vendor-chunks/html-to-text@9.0.5","vendor-chunks/domelementtype@2.3.0","vendor-chunks/deepmerge@4.3.1","vendor-chunks/@selderee+plugin-htmlparser2@0.11.0","vendor-chunks/@react-email+render@0.0.16_react-dom@18.3.1_react@18.3.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact%2Froute&page=%2Fapi%2Fcontact%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();