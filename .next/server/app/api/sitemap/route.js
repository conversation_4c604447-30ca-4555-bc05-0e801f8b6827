(()=>{var e={};e.id=436,e.ids=[436],e.modules={7713:(e,t,r)=>{var n={"./en.json":[9316,316],"./es.json":[5671,671],"./ja.json":[2171,171],"./zh-CN.json":[6074,74]};function o(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],o=t[0];return r.e(t[1]).then(()=>r.t(o,19))}o.keys=()=>Object.keys(n),o.id=7713,e.exports=o},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5592:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>f});var n={};r.r(n),r.d(n,{GET:()=>c});var o=r(8738),i=r(3163),a=r(9803),u=r(5950),s=r(2665);let l=r(6528).uO;async function c(){try{let e=s.ep.url,t=[];Object.keys(l).forEach(r=>{(0,s._G)(r).forEach(n=>{let o=Object.keys(l).map(t=>{let o="en"===t?"":`/${t}`,i=n.url.replace(e,"").replace(`/${r}`,"");return{hreflang:"zh-CN"===t?"zh-Hans":t,href:`${e}${o}${i}`}});t.push({...n,alternates:o})})});let r=t.reduce((t,r)=>{let n=r.url.replace(e,""),o=t.find(t=>{let r=t.url.replace(e,"");return r===n||r.replace(/^\/[a-z-]+/,"")===n.replace(/^\/[a-z-]+/,"")});return o?o.alternates&&r.alternates&&(o.alternates=[...o.alternates,...r.alternates].filter((e,t,r)=>t===r.findIndex(t=>t.hreflang===e.hreflang))):t.push(r),t},[]),n=function(e){let t=e.map(e=>{let t=e.alternates?e.alternates.map(e=>`    <xhtml:link rel="alternate" hreflang="${e.hreflang}" href="${e.href}" />`).join("\n"):"";return`  <url>
    <loc>${e.url}</loc>
    <lastmod>${e.lastModified}</lastmod>
    <changefreq>${e.changefreq}</changefreq>
    <priority>${e.priority}</priority>
${t}
  </url>`}).join("\n");return`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${t}
</urlset>`}(r);return new u.NextResponse(n,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(e){return new u.NextResponse("Internal Server Error",{status:500,headers:{"Content-Type":"text/plain"}})}}let d=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/sitemap/route",pathname:"/api/sitemap",filename:"route",bundlePath:"app/api/sitemap/route"},resolvedPagePath:"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:p,staticGenerationAsyncStorage:f,serverHooks:m}=d,g="/api/sitemap/route";function h(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:f})}},5798:(e,t,r)=>{"use strict";var n=r(7138);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},7138:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(333),o=r(5142);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5142:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6286:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},333:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return s},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return l}});let o=r(4580),i=r(2934),a=r(6286),u="NEXT_REDIRECT";function s(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(u);n.digest=u+";"+t+";"+e+";"+r+";";let i=o.requestAsyncStorage.getStore();return i&&(n.mutableCookies=i.mutableCookies),n}function l(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw s(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw s(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,o]=e.digest.split(";",4),i=Number(o);return t===u&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(i)&&i in a.RedirectStatusCode}function p(e){return d(e)?e.digest.split(";",3)[2]:null}function f(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6528:(e,t,r)=>{"use strict";r.d(t,{uO:()=>i,ZP:()=>a});var n=r(5798);let o=["en","zh-CN","ja","es"],i={en:{name:"English",nativeName:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8",dir:"ltr"},"zh-CN":{name:"Chinese (Simplified)",nativeName:"简体中文",flag:"\uD83C\uDDE8\uD83C\uDDF3",dir:"ltr"},ja:{name:"Japanese",nativeName:"日本語",flag:"\uD83C\uDDEF\uD83C\uDDF5",dir:"ltr"},es:{name:"Spanish",nativeName:"Espa\xf1ol",flag:"\uD83C\uDDEA\uD83C\uDDF8",dir:"ltr"}},a=async({locale:e})=>{o.includes(e)||(0,n.notFound)();try{return{messages:(await r(7713)(`./${e}.json`)).default,timeZone:"UTC",now:new Date,formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"},long:{day:"numeric",month:"long",year:"numeric",hour:"numeric",minute:"numeric"}},number:{precise:{maximumFractionDigits:5}},list:{enumeration:{style:"long",type:"conjunction"}}}}}catch(e){(0,n.notFound)()}}},2665:(e,t,r)=>{"use strict";r.d(t,{_G:()=>o,ep:()=>n,xU:()=>i});let n={name:"Tucsenberg",title:"Tucsenberg - Professional Flood Protection Equipment Manufacturer",description:"Tucsenberg specializes in flood protection equipment R&D, design and manufacturing, providing high-quality flood protection products and comprehensive solutions for global users.",url:process.env.NEXT_PUBLIC_SITE_URL||"https://tucsenberg.com",ogImage:"/images/og-image.jpg",twitterHandle:"@tucsenberg",keywords:["flood protection","flood barriers","flood control","emergency flood protection","industrial flood protection","water pumps","drainage systems","emergency equipment","Tucsenberg"]};function o(e="en"){let t=n.url,r="en"===e?"":`/${e}`;return[{url:"",priority:1,changefreq:"daily"},{url:"/about",priority:.8,changefreq:"monthly"},{url:"/contact",priority:.8,changefreq:"monthly"},{url:"/products",priority:.9,changefreq:"weekly"},{url:"/news",priority:.7,changefreq:"weekly"},{url:"/support",priority:.6,changefreq:"monthly"}].map(e=>({url:`${t}${r}${e.url}`,lastModified:new Date().toISOString(),priority:e.priority,changefreq:e.changefreq}))}function i(){let e=n.url;return`User-agent: *
Allow: /

# Sitemaps
Sitemap: ${e}/sitemap.xml

# Crawl-delay
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /private/

# Allow specific API endpoints
Allow: /api/sitemap
Allow: /api/robots
`}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[999,946],()=>r(5592));module.exports=n})();