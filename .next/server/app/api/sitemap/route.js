/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sitemap/route";
exports.ids = ["app/api/sitemap/route"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./zh-CN.json": [
		"(rsc)/./messages/zh-CN.json",
		"_rsc_messages_zh-CN_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsitemap%2Froute&page=%2Fapi%2Fsitemap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsitemap%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsitemap%2Froute&page=%2Fapi%2Fsitemap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsitemap%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_Data_Vibe_Code_test_web_1_0_src_app_api_sitemap_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/sitemap/route.ts */ \"(rsc)/./src/app/api/sitemap/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sitemap/route\",\n        pathname: \"/api/sitemap\",\n        filename: \"route\",\n        bundlePath: \"app/api/sitemap/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts\",\n    nextConfigOutput,\n    userland: _Users_Data_Vibe_Code_test_web_1_0_src_app_api_sitemap_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/sitemap/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX0BvcGVudGVsZW1ldHJ5K2FwaUAxLjkuMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZzaXRlbWFwJTJGcm91dGUmcGFnZT0lMkZhcGklMkZzaXRlbWFwJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc2l0ZW1hcCUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRkRhdGElMkZWaWJlLUNvZGUlMkZ0ZXN0LXdlYi0xLjAlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGRGF0YSUyRlZpYmUtQ29kZSUyRnRlc3Qtd2ViLTEuMCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD1zdGFuZGFsb25lJnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ2U7QUFDNUY7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90dWNzZW5iZXJnLXdlYnNpdGUvPzIyMjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL1VzZXJzL0RhdGEvVmliZS1Db2RlL3Rlc3Qtd2ViLTEuMC9zcmMvYXBwL2FwaS9zaXRlbWFwL3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcInN0YW5kYWxvbmVcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvc2l0ZW1hcC9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3NpdGVtYXBcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3NpdGVtYXAvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvVXNlcnMvRGF0YS9WaWJlLUNvZGUvdGVzdC13ZWItMS4wL3NyYy9hcHAvYXBpL3NpdGVtYXAvcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL3NpdGVtYXAvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsitemap%2Froute&page=%2Fapi%2Fsitemap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsitemap%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/sitemap/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/sitemap/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_seo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/seo */ \"(rsc)/./src/lib/seo.ts\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(rsc)/./src/lib/i18n.ts\");\n\n\n\n/**\n * GET /api/sitemap\n * 生成动态 sitemap.xml\n */ async function GET() {\n    try {\n        const baseUrl = _lib_seo__WEBPACK_IMPORTED_MODULE_1__.SITE_CONFIG.url;\n        // 生成所有语言的 URL\n        const allUrls = [];\n        // 为每种语言生成 URL\n        Object.keys(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.SUPPORTED_LOCALES).forEach((locale)=>{\n            const urls = (0,_lib_seo__WEBPACK_IMPORTED_MODULE_1__.generateSitemapUrls)(locale);\n            urls.forEach((urlData)=>{\n                // 生成多语言替代链接\n                const alternates = Object.keys(_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.SUPPORTED_LOCALES).map((altLocale)=>{\n                    const altPrefix = altLocale === \"en\" ? \"\" : `/${altLocale}`;\n                    const path = urlData.url.replace(baseUrl, \"\").replace(`/${locale}`, \"\");\n                    return {\n                        hreflang: altLocale === \"zh-CN\" ? \"zh-Hans\" : altLocale,\n                        href: `${baseUrl}${altPrefix}${path}`\n                    };\n                });\n                allUrls.push({\n                    ...urlData,\n                    alternates\n                });\n            });\n        });\n        // 去重（基于路径）\n        const uniqueUrls = allUrls.reduce((acc, current)=>{\n            const path = current.url.replace(baseUrl, \"\");\n            const existing = acc.find((item)=>{\n                const existingPath = item.url.replace(baseUrl, \"\");\n                return existingPath === path || existingPath.replace(/^\\/[a-z-]+/, \"\") === path.replace(/^\\/[a-z-]+/, \"\");\n            });\n            if (!existing) {\n                acc.push(current);\n            } else {\n                // 合并多语言链接\n                if (existing.alternates && current.alternates) {\n                    existing.alternates = [\n                        ...existing.alternates,\n                        ...current.alternates\n                    ].filter((alt, index, self)=>index === self.findIndex((a)=>a.hreflang === alt.hreflang));\n                }\n            }\n            return acc;\n        }, []);\n        // 生成 XML\n        const sitemap = generateSitemapXML(uniqueUrls);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(sitemap, {\n            status: 200,\n            headers: {\n                \"Content-Type\": \"application/xml\",\n                \"Cache-Control\": \"public, max-age=3600, s-maxage=3600\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Sitemap generation error:\", error);\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(\"Internal Server Error\", {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"text/plain\"\n            }\n        });\n    }\n}\n/**\n * 生成 sitemap XML 内容\n */ function generateSitemapXML(urls) {\n    const xmlUrls = urls.map((urlData)=>{\n        const alternateLinks = urlData.alternates ? urlData.alternates.map((alt)=>`    <xhtml:link rel=\"alternate\" hreflang=\"${alt.hreflang}\" href=\"${alt.href}\" />`).join(\"\\n\") : \"\";\n        return `  <url>\n    <loc>${urlData.url}</loc>\n    <lastmod>${urlData.lastModified}</lastmod>\n    <changefreq>${urlData.changefreq}</changefreq>\n    <priority>${urlData.priority}</priority>\n${alternateLinks}\n  </url>`;\n    }).join(\"\\n\");\n    return `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"\n        xmlns:xhtml=\"http://www.w3.org/1999/xhtml\">\n${xmlUrls}\n</urlset>`;\n}\n/**\n * 生成 sitemap 索引（如果需要多个 sitemap 文件）\n */ function generateSitemapIndex() {\n    const baseUrl = _lib_seo__WEBPACK_IMPORTED_MODULE_1__.SITE_CONFIG.url;\n    const now = new Date().toISOString();\n    return `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n  <sitemap>\n    <loc>${baseUrl}/api/sitemap</loc>\n    <lastmod>${now}</lastmod>\n  </sitemap>\n</sitemapindex>`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/sitemap/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LOCALE_CONFIG: () => (/* binding */ LOCALE_CONFIG),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   getLocaleConfig: () => (/* binding */ getLocaleConfig),\n/* harmony export */   getLocaleDisplayName: () => (/* binding */ getLocaleDisplayName),\n/* harmony export */   isValidLocale: () => (/* binding */ isValidLocale),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@4.1.0_next@14.2.30_react@18.3.1_typescript@5.8.3/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\");\n\n\n// 支持的语言列表\nconst locales = [\n    \"en\",\n    \"zh-CN\",\n    \"ja\",\n    \"es\"\n];\n// 默认语言\nconst defaultLocale = \"en\";\n// 语言配置\nconst LOCALE_CONFIG = {\n    \"en\": {\n        name: \"English\",\n        nativeName: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        dir: \"ltr\"\n    },\n    \"zh-CN\": {\n        name: \"Chinese (Simplified)\",\n        nativeName: \"简体中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        dir: \"ltr\"\n    },\n    \"ja\": {\n        name: \"Japanese\",\n        nativeName: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        dir: \"ltr\"\n    },\n    \"es\": {\n        name: \"Spanish\",\n        nativeName: \"Espa\\xf1ol\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        dir: \"ltr\"\n    }\n};\n// next-intl 配置\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证语言是否支持\n    if (!locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        // 动态导入翻译文件\n        const messages = (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default;\n        return {\n            messages,\n            // 时区配置\n            timeZone: \"UTC\",\n            // 现在时间（用于相对时间格式化）\n            now: new Date(),\n            // 格式化配置\n            formats: {\n                dateTime: {\n                    short: {\n                        day: \"numeric\",\n                        month: \"short\",\n                        year: \"numeric\"\n                    },\n                    long: {\n                        day: \"numeric\",\n                        month: \"long\",\n                        year: \"numeric\",\n                        hour: \"numeric\",\n                        minute: \"numeric\"\n                    }\n                },\n                number: {\n                    precise: {\n                        maximumFractionDigits: 5\n                    }\n                },\n                list: {\n                    enumeration: {\n                        style: \"long\",\n                        type: \"conjunction\"\n                    }\n                }\n            }\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 工具函数：检查是否为支持的语言\nfunction isValidLocale(locale) {\n    return locales.includes(locale);\n}\n// 工具函数：获取语言配置\nfunction getLocaleConfig(locale) {\n    return LOCALE_CONFIG[locale];\n}\n// 工具函数：获取语言的显示名称\nfunction getLocaleDisplayName(locale, displayLocale = \"en\") {\n    const config = LOCALE_CONFIG[locale];\n    return displayLocale === locale ? config.nativeName : config.name;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_LOCALE: () => (/* binding */ DEFAULT_LOCALE),\n/* harmony export */   FormatUtils: () => (/* binding */ FormatUtils),\n/* harmony export */   I18nUtils: () => (/* binding */ I18nUtils),\n/* harmony export */   LOCALES: () => (/* binding */ SUPPORTED_LOCALES),\n/* harmony export */   SEOUtils: () => (/* binding */ SEOUtils),\n/* harmony export */   SUPPORTED_LOCALES: () => (/* binding */ SUPPORTED_LOCALES),\n/* harmony export */   getLanguageSwitcherData: () => (/* binding */ getLanguageSwitcherData),\n/* harmony export */   useI18n: () => (/* binding */ useI18n),\n/* harmony export */   useLocale: () => (/* reexport safe */ next_intl__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   useTranslations: () => (/* reexport safe */ next_intl__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/.pnpm/next-intl@4.1.0_next@14.2.30_react@18.3.1_typescript@5.8.3/node_modules/next-intl/dist/esm/development/react-server/useTranslations.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/.pnpm/next-intl@4.1.0_next@14.2.30_react@18.3.1_typescript@5.8.3/node_modules/next-intl/dist/esm/development/react-server/useLocale.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../i18n */ \"(rsc)/./src/i18n.ts\");\n\n\nconst SUPPORTED_LOCALES = _i18n__WEBPACK_IMPORTED_MODULE_0__.LOCALE_CONFIG;\nconst DEFAULT_LOCALE = _i18n__WEBPACK_IMPORTED_MODULE_0__.defaultLocale;\n// 语言检测和切换工具\nclass I18nUtils {\n    /**\n   * 获取当前语言信息（仅在服务端使用）\n   */ static getCurrentLocale() {\n        if (true) return DEFAULT_LOCALE;\n        // 在客户端，应该使用 useI18n Hook 来获取当前语言\n        return DEFAULT_LOCALE;\n    }\n    /**\n   * 获取语言配置\n   */ static getLocaleConfig(locale) {\n        return SUPPORTED_LOCALES[locale] || SUPPORTED_LOCALES[DEFAULT_LOCALE];\n    }\n    /**\n   * 获取所有支持的语言\n   */ static getSupportedLocales() {\n        return Object.keys(SUPPORTED_LOCALES);\n    }\n    /**\n   * 检查是否为支持的语言\n   */ static isSupportedLocale(locale) {\n        return locale in SUPPORTED_LOCALES;\n    }\n    /**\n   * 切换语言（仅在服务端使用，客户端请使用 useI18n Hook）\n   */ static switchLocale(locale, asPath) {\n        if (true) return;\n        // 在客户端，应该使用 useI18n Hook 来切换语言\n        console.warn(\"I18nUtils.switchLocale should not be used on client side. Use useI18n hook instead.\");\n    }\n    /**\n   * 获取本地化的URL\n   */ static getLocalizedUrl(path, locale) {\n        if (locale === DEFAULT_LOCALE) {\n            return path;\n        }\n        return `/${locale}${path}`;\n    }\n    /**\n   * 从URL中提取语言\n   */ static extractLocaleFromPath(path) {\n        const segments = path.split(\"/\").filter(Boolean);\n        const firstSegment = segments[0];\n        if (this.isSupportedLocale(firstSegment)) {\n            return {\n                locale: firstSegment,\n                cleanPath: \"/\" + segments.slice(1).join(\"/\")\n            };\n        }\n        return {\n            locale: DEFAULT_LOCALE,\n            cleanPath: path\n        };\n    }\n}\n// 格式化工具\nclass FormatUtils {\n    /**\n   * 格式化货币\n   */ static formatCurrency(amount, locale = DEFAULT_LOCALE, currency) {\n        const config = SUPPORTED_LOCALES[locale];\n        const currencyCode = currency || config.currency;\n        return new Intl.NumberFormat(config.numberFormat, {\n            style: \"currency\",\n            currency: currencyCode,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(amount);\n    }\n    /**\n   * 格式化数字\n   */ static formatNumber(number, locale = DEFAULT_LOCALE, options) {\n        const config = SUPPORTED_LOCALES[locale];\n        return new Intl.NumberFormat(config.numberFormat, options).format(number);\n    }\n    /**\n   * 格式化日期\n   */ static formatDate(date, locale = DEFAULT_LOCALE, options) {\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        const config = SUPPORTED_LOCALES[locale];\n        const defaultOptions = {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        };\n        return new Intl.DateTimeFormat(config.numberFormat, {\n            ...defaultOptions,\n            ...options\n        }).format(dateObj);\n    }\n    /**\n   * 格式化相对时间\n   */ static formatRelativeTime(date, locale = DEFAULT_LOCALE) {\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        const now = new Date();\n        const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n        const config = SUPPORTED_LOCALES[locale];\n        const rtf = new Intl.RelativeTimeFormat(config.numberFormat, {\n            numeric: \"auto\"\n        });\n        // 计算时间差\n        if (diffInSeconds < 60) {\n            return rtf.format(-diffInSeconds, \"second\");\n        } else if (diffInSeconds < 3600) {\n            return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n        } else if (diffInSeconds < 86400) {\n            return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n        } else if (diffInSeconds < 2592000) {\n            return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n        } else if (diffInSeconds < 31536000) {\n            return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n        } else {\n            return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n        }\n    }\n}\n// next-intl 翻译工具Hook\nfunction useI18n() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const localeConfig = SUPPORTED_LOCALES[locale];\n    return {\n        t,\n        locale,\n        localeConfig,\n        switchLocale: (newLocale)=>{\n            if (false) {}\n        },\n        formatCurrency: (amount, currency)=>FormatUtils.formatCurrency(amount, locale, currency),\n        formatNumber: (number, options)=>FormatUtils.formatNumber(number, locale, options),\n        formatDate: (date, options)=>FormatUtils.formatDate(date, locale, options),\n        formatRelativeTime: (date)=>FormatUtils.formatRelativeTime(date, locale)\n    };\n}\n// 服务端翻译工具（重新导出 next-intl 的函数）\n\n// 语言切换组件数据\nfunction getLanguageSwitcherData(currentLocale) {\n    return Object.entries(SUPPORTED_LOCALES).map(([code, config])=>({\n            code: code,\n            name: config.name,\n            nativeName: config.nativeName,\n            flag: config.flag,\n            active: code === currentLocale\n        }));\n}\n// SEO相关的语言工具\nclass SEOUtils {\n    /**\n   * 生成hreflang标签\n   */ static generateHrefLangTags(path) {\n        return Object.keys(SUPPORTED_LOCALES).map((locale)=>({\n                hrefLang: locale === \"zh-CN\" ? \"zh-Hans\" : locale,\n                href: I18nUtils.getLocalizedUrl(path, locale)\n            }));\n    }\n    /**\n   * 获取本地化的元数据\n   */ static getLocalizedMetadata(baseMetadata, locale) {\n        const config = SUPPORTED_LOCALES[locale];\n        return {\n            ...baseMetadata,\n            language: locale,\n            locale: locale,\n            alternateLanguages: Object.keys(SUPPORTED_LOCALES).reduce((acc, loc)=>{\n                acc[loc] = I18nUtils.getLocalizedUrl(\"/\", loc);\n                return acc;\n            }, {})\n        };\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/seo.ts":
/*!************************!*\
  !*** ./src/lib/seo.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LOCALE_CONFIG: () => (/* binding */ LOCALE_CONFIG),\n/* harmony export */   SITE_CONFIG: () => (/* binding */ SITE_CONFIG),\n/* harmony export */   generateBreadcrumbStructuredData: () => (/* binding */ generateBreadcrumbStructuredData),\n/* harmony export */   generatePageMetadata: () => (/* binding */ generatePageMetadata),\n/* harmony export */   generateRobotsTxt: () => (/* binding */ generateRobotsTxt),\n/* harmony export */   generateSitemapUrls: () => (/* binding */ generateSitemapUrls),\n/* harmony export */   generateStructuredData: () => (/* binding */ generateStructuredData)\n/* harmony export */ });\n// 网站基础信息\nconst SITE_CONFIG = {\n    name: \"Tucsenberg\",\n    title: \"Tucsenberg - Professional Flood Protection Equipment Manufacturer\",\n    description: \"Tucsenberg specializes in flood protection equipment R&D, design and manufacturing, providing high-quality flood protection products and comprehensive solutions for global users.\",\n    url: process.env.NEXT_PUBLIC_SITE_URL || \"https://tucsenberg.com\",\n    ogImage: \"/images/og-image.jpg\",\n    twitterHandle: \"@tucsenberg\",\n    keywords: [\n        \"flood protection\",\n        \"flood barriers\",\n        \"flood control\",\n        \"emergency flood protection\",\n        \"industrial flood protection\",\n        \"water pumps\",\n        \"drainage systems\",\n        \"emergency equipment\",\n        \"Tucsenberg\"\n    ]\n};\n// 语言配置\nconst LOCALE_CONFIG = {\n    \"en\": {\n        name: \"English\",\n        hreflang: \"en\",\n        dir: \"ltr\"\n    },\n    \"zh-CN\": {\n        name: \"简体中文\",\n        hreflang: \"zh-Hans\",\n        dir: \"ltr\"\n    },\n    \"ja\": {\n        name: \"日本語\",\n        hreflang: \"ja\",\n        dir: \"ltr\"\n    },\n    \"es\": {\n        name: \"Espa\\xf1ol\",\n        hreflang: \"es\",\n        dir: \"ltr\"\n    }\n};\n/**\n * 生成页面元数据\n */ function generatePageMetadata({ title, description, keywords, locale = \"en\", path = \"\", image, noIndex = false, type = \"website\" }) {\n    const fullTitle = title ? `${title} | ${SITE_CONFIG.name}` : SITE_CONFIG.title;\n    const fullDescription = description || SITE_CONFIG.description;\n    const fullKeywords = keywords ? [\n        ...SITE_CONFIG.keywords,\n        ...keywords\n    ] : SITE_CONFIG.keywords;\n    const fullUrl = `${SITE_CONFIG.url}${path}`;\n    const fullImage = image ? `${SITE_CONFIG.url}${image}` : `${SITE_CONFIG.url}${SITE_CONFIG.ogImage}`;\n    // 生成 hreflang 链接\n    const alternateLanguages = {};\n    Object.entries(LOCALE_CONFIG).forEach(([code, config])=>{\n        const localePath = code === \"en\" ? path : `/${code}${path}`;\n        alternateLanguages[config.hreflang] = `${SITE_CONFIG.url}${localePath}`;\n    });\n    return {\n        title: fullTitle,\n        description: fullDescription,\n        keywords: fullKeywords.join(\", \"),\n        authors: [\n            {\n                name: SITE_CONFIG.name\n            }\n        ],\n        creator: SITE_CONFIG.name,\n        publisher: SITE_CONFIG.name,\n        robots: noIndex ? \"noindex, nofollow\" : \"index, follow\",\n        // Open Graph\n        openGraph: {\n            type,\n            locale: LOCALE_CONFIG[locale].hreflang,\n            url: fullUrl,\n            title: fullTitle,\n            description: fullDescription,\n            siteName: SITE_CONFIG.name,\n            images: [\n                {\n                    url: fullImage,\n                    width: 1200,\n                    height: 630,\n                    alt: fullTitle\n                }\n            ]\n        },\n        // Twitter\n        twitter: {\n            card: \"summary_large_image\",\n            site: SITE_CONFIG.twitterHandle,\n            creator: SITE_CONFIG.twitterHandle,\n            title: fullTitle,\n            description: fullDescription,\n            images: [\n                fullImage\n            ]\n        },\n        // 多语言链接\n        alternates: {\n            canonical: fullUrl,\n            languages: alternateLanguages\n        },\n        // 其他元数据\n        other: {\n            \"og:locale:alternate\": Object.values(LOCALE_CONFIG).filter((config)=>config.hreflang !== LOCALE_CONFIG[locale].hreflang).map((config)=>config.hreflang).join(\",\")\n        }\n    };\n}\n/**\n * 生成结构化数据 (JSON-LD)\n */ function generateStructuredData({ type, data, locale = \"en\" }) {\n    const baseUrl = SITE_CONFIG.url;\n    const localeConfig = LOCALE_CONFIG[locale];\n    const baseStructuredData = {\n        \"@context\": \"https://schema.org\",\n        \"@language\": localeConfig.hreflang\n    };\n    switch(type){\n        case \"Organization\":\n            return {\n                ...baseStructuredData,\n                \"@type\": \"Organization\",\n                name: SITE_CONFIG.name,\n                url: baseUrl,\n                logo: `${baseUrl}/images/logo.png`,\n                description: SITE_CONFIG.description,\n                contactPoint: {\n                    \"@type\": \"ContactPoint\",\n                    telephone: \"******-FLOOD-PROTECTION\",\n                    contactType: \"customer service\",\n                    availableLanguage: Object.values(LOCALE_CONFIG).map((config)=>config.name),\n                    areaServed: \"Worldwide\"\n                },\n                sameAs: [\n                    \"https://linkedin.com/company/tucsenberg\",\n                    \"https://twitter.com/tucsenberg\",\n                    \"https://facebook.com/tucsenberg\"\n                ],\n                ...data\n            };\n        case \"WebSite\":\n            return {\n                ...baseStructuredData,\n                \"@type\": \"WebSite\",\n                name: SITE_CONFIG.name,\n                url: baseUrl,\n                description: SITE_CONFIG.description,\n                inLanguage: localeConfig.hreflang,\n                potentialAction: {\n                    \"@type\": \"SearchAction\",\n                    target: `${baseUrl}/search?q={search_term_string}`,\n                    \"query-input\": \"required name=search_term_string\"\n                },\n                ...data\n            };\n        case \"Product\":\n            return {\n                ...baseStructuredData,\n                \"@type\": \"Product\",\n                manufacturer: {\n                    \"@type\": \"Organization\",\n                    name: SITE_CONFIG.name\n                },\n                ...data\n            };\n        case \"Article\":\n            return {\n                ...baseStructuredData,\n                \"@type\": \"Article\",\n                publisher: {\n                    \"@type\": \"Organization\",\n                    name: SITE_CONFIG.name,\n                    logo: {\n                        \"@type\": \"ImageObject\",\n                        url: `${baseUrl}/images/logo.png`\n                    }\n                },\n                ...data\n            };\n        case \"ContactPage\":\n            return {\n                ...baseStructuredData,\n                \"@type\": \"ContactPage\",\n                name: \"Contact Us\",\n                url: `${baseUrl}/contact`,\n                description: \"Contact Tucsenberg for flood protection solutions and emergency equipment.\",\n                mainEntity: {\n                    \"@type\": \"Organization\",\n                    name: SITE_CONFIG.name,\n                    contactPoint: [\n                        {\n                            \"@type\": \"ContactPoint\",\n                            telephone: \"******-FLOOD-PROTECTION\",\n                            contactType: \"customer service\",\n                            availableLanguage: Object.values(LOCALE_CONFIG).map((config)=>config.name)\n                        },\n                        {\n                            \"@type\": \"ContactPoint\",\n                            email: \"<EMAIL>\",\n                            contactType: \"customer service\"\n                        }\n                    ]\n                },\n                ...data\n            };\n        default:\n            return baseStructuredData;\n    }\n}\n/**\n * 生成面包屑导航结构化数据\n */ function generateBreadcrumbStructuredData(breadcrumbs, locale = \"en\") {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"BreadcrumbList\",\n        \"@language\": LOCALE_CONFIG[locale].hreflang,\n        itemListElement: breadcrumbs.map((item, index)=>({\n                \"@type\": \"ListItem\",\n                position: index + 1,\n                name: item.name,\n                item: `${SITE_CONFIG.url}${item.url}`\n            }))\n    };\n}\n/**\n * 生成 sitemap 数据\n */ function generateSitemapUrls(locale = \"en\") {\n    const baseUrl = SITE_CONFIG.url;\n    const localePrefix = locale === \"en\" ? \"\" : `/${locale}`;\n    const staticPages = [\n        {\n            url: \"\",\n            priority: 1.0,\n            changefreq: \"daily\"\n        },\n        {\n            url: \"/about\",\n            priority: 0.8,\n            changefreq: \"monthly\"\n        },\n        {\n            url: \"/contact\",\n            priority: 0.8,\n            changefreq: \"monthly\"\n        },\n        {\n            url: \"/products\",\n            priority: 0.9,\n            changefreq: \"weekly\"\n        },\n        {\n            url: \"/news\",\n            priority: 0.7,\n            changefreq: \"weekly\"\n        },\n        {\n            url: \"/support\",\n            priority: 0.6,\n            changefreq: \"monthly\"\n        }\n    ];\n    return staticPages.map((page)=>({\n            url: `${baseUrl}${localePrefix}${page.url}`,\n            lastModified: new Date().toISOString(),\n            priority: page.priority,\n            changefreq: page.changefreq\n        }));\n}\n/**\n * 生成 robots.txt 内容\n */ function generateRobotsTxt() {\n    const baseUrl = SITE_CONFIG.url;\n    return `User-agent: *\nAllow: /\n\n# Sitemaps\nSitemap: ${baseUrl}/sitemap.xml\n\n# Crawl-delay\nCrawl-delay: 1\n\n# Disallow admin and private areas\nDisallow: /admin/\nDisallow: /api/\nDisallow: /_next/\nDisallow: /private/\n\n# Allow specific API endpoints\nAllow: /api/sitemap\nAllow: /api/robots\n`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/seo.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/next-intl@4.1.0_next@14.2.30_react@18.3.1_typescript@5.8.3","vendor-chunks/@formatjs+icu-messageformat-parser@2.11.2","vendor-chunks/@formatjs+icu-skeleton-parser@1.8.14","vendor-chunks/intl-messageformat@10.7.16","vendor-chunks/use-intl@4.1.0_react@18.3.1","vendor-chunks/tslib@2.8.1","vendor-chunks/@formatjs+fast-memoize@2.2.7"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.30_@opentelemetry+api@1.9.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsitemap%2Froute&page=%2Fapi%2Fsitemap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsitemap%2Froute.ts&appDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2FData%2FVibe-Code%2Ftest-web-1.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();